<?php include('session.php'); ?>
<?php include 'header.php'; ?>
<?php
// Include the database connection file
require_once 'db_connection.php';

// Check if ID parameter is set
if(isset($_GET['id'])) {
    $id = $_GET['id'];

    // Fetch record from database for the given ID
    $sql = "SELECT * FROM tokenslip WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Output data of each row
        $row = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Token Slip</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .header-section .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 10px;
            position: relative;
            z-index: 1;
        }

        .form-section {
            padding: 40px;
        }

        .section-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
            outline: none;
        }

        .input-group {
            position: relative;
        }

        .input-group .form-control {
            padding-left: 45px;
        }

        .input-group-text {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--secondary-color);
            z-index: 3;
            font-size: 1.1rem;
        }

        .btn-submit {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
            color: white;
        }

        .btn-submit:active {
            transform: translateY(0);
        }

        .btn-submit::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-submit:hover::before {
            left: 100%;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-section {
                padding: 20px;
            }

            .header-section h1 {
                font-size: 2rem;
            }

            .form-section {
                padding: 25px;
            }

            .section-card {
                padding: 20px;
            }

            .form-control, .form-select {
                padding: 10px 12px;
            }

            .input-group .form-control {
                padding-left: 40px;
            }
        }

        @media (max-width: 480px) {
            .header-section h1 {
                font-size: 1.8rem;
            }

            .form-section {
                padding: 20px;
            }

            .section-card {
                padding: 15px;
            }
        }

        /* Animation */
        .section-card {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .section-card:nth-child(1) { animation-delay: 0.1s; }
        .section-card:nth-child(2) { animation-delay: 0.2s; }
        .section-card:nth-child(3) { animation-delay: 0.3s; }
        .section-card:nth-child(4) { animation-delay: 0.4s; }
        .section-card:nth-child(5) { animation-delay: 0.5s; }
        .section-card:nth-child(6) { animation-delay: 0.6s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading state */
        .btn-submit.loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .btn-submit.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .current-image {
            max-width: 200px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            margin-top: 15px;
        }
    </style>
</head>
<body>

<div class="main-container">
    <!-- Header Section -->
    <div class="header-section">
        <h1><i class="fas fa-edit me-3"></i>Edit Token Slip</h1>
        <p class="subtitle">Update Pakistan Identity Card Application Details</p>
    </div>

    <!-- Form Section -->
    <div class="form-section">
        <form action="update.php" method="post" enctype="multipart/form-data" id="editForm">
            <input type="hidden" name="id" value="<?php echo htmlspecialchars($row['id']); ?>">

            <!-- Basic Information Section -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-user-circle"></i>
                    Basic Information
                </h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="tracking-id">
                                <i class="fas fa-barcode"></i>
                                Tracking ID
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-barcode"></i></span>
                                <input type="text" class="form-control" id="tracking-id" name="tracking-id" value="<?php echo htmlspecialchars($row['tracking_id']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="name">
                                <i class="fas fa-user"></i>
                                Name
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($row['name']); ?>" required>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="cnic-number">
                                <i class="fas fa-id-card"></i>
                                CNIC Number
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                <input type="text" class="form-control" id="cnic-number" name="cnic-number" value="<?php echo htmlspecialchars($row['cnic_number']); ?>" maxlength="15" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="date-of-birth">
                                <i class="fas fa-calendar-alt"></i>
                                Date of Birth
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                <input type="date" class="form-control" id="date-of-birth" name="date-of-birth" value="<?php echo htmlspecialchars($row['date_of_birth']); ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Application Details Section -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-file-alt"></i>
                    Application Details
                </h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="application-type">
                                <i class="fas fa-file-alt"></i>
                                Application Type
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-file-alt"></i></span>
                                <input type="text" class="form-control" id="application-type" name="application-type" value="<?php echo htmlspecialchars($row['application_type']); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="priority">
                                <i class="fas fa-star"></i>
                                Priority
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-star"></i></span>
                                <input type="text" class="form-control" id="priority" name="priority" value="<?php echo htmlspecialchars($row['priority']); ?>" required>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="date">
                                <i class="fas fa-calendar-alt"></i>
                                Date
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                <input type="date" class="form-control" id="date" name="date" value="<?php echo htmlspecialchars($row['date']); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="time">
                                <i class="fas fa-clock"></i>
                                Time
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                <input type="time" class="form-control" id="time" name="time" value="<?php echo htmlspecialchars($row['time']); ?>" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-address-book"></i>
                    Contact Information
                </h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="mobile-no">
                                <i class="fas fa-phone"></i>
                                Mobile Number
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                <input type="tel" class="form-control" id="mobile-no" name="mobile-no" value="<?php echo htmlspecialchars($row['mobile_number']); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="email-id">
                                <i class="fas fa-envelope"></i>
                                Email ID
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="email-id" name="email-id" value="<?php echo htmlspecialchars($row['email_id']); ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Family Information Section -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-users"></i>
                    Family Information
                </h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="father-name">
                                <i class="fas fa-male"></i>
                                Father Name
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-male"></i></span>
                                <input type="text" class="form-control" id="father-name" name="father-name" value="<?php echo htmlspecialchars($row['father_name']); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="father-cnic-no">
                                <i class="fas fa-id-card"></i>
                                Father CNIC Number
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                <input type="text" class="form-control" id="father-cnic-no" name="father-cnic-no" value="<?php echo htmlspecialchars($row['father_cnic_number']); ?>" maxlength="15">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="mother-name">
                                <i class="fas fa-female"></i>
                                Mother Name
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-female"></i></span>
                                <input type="text" class="form-control" id="mother-name" name="mother-name" value="<?php echo htmlspecialchars($row['mother_name']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="mother-cnic-no">
                                <i class="fas fa-id-card"></i>
                                Mother CNIC Number
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                <input type="text" class="form-control" id="mother-cnic-no" name="mother-cnic-no" value="<?php echo htmlspecialchars($row['mother_cnic_number']); ?>" maxlength="15">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="spouse-name">
                                <i class="fas fa-heart"></i>
                                Spouse Name
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-heart"></i></span>
                                <input type="text" class="form-control" id="spouse-name" name="spouse-name" value="<?php echo htmlspecialchars($row['spouse_name']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="spouse-cnic-no">
                                <i class="fas fa-id-card"></i>
                                Spouse CNIC Number
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                <input type="text" class="form-control" id="spouse-cnic-no" name="spouse-cnic-no" value="<?php echo htmlspecialchars($row['spouse_cnic_number']); ?>" maxlength="15">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address Section -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-map-marker-alt"></i>
                    Address Information
                </h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="present-address">
                                <i class="fas fa-home"></i>
                                Present Address
                            </label>
                            <textarea class="form-control" id="present-address" name="present-address" rows="3" placeholder="Enter present address"><?php echo htmlspecialchars($row['present_address']); ?></textarea>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="permanent-address">
                                <i class="fas fa-building"></i>
                                Permanent Address
                            </label>
                            <textarea class="form-control" id="permanent-address" name="permanent-address" rows="3" placeholder="Enter permanent address"><?php echo htmlspecialchars($row['permanent_address']); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Picture Upload Section -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-camera"></i>
                    Picture Upload
                </h3>
                <div class="form-group">
                    <label class="form-label" for="picture">
                        <i class="fas fa-camera"></i>
                        Upload New Picture
                    </label>
                    <input type="file" class="form-control" id="picture" name="picture" accept="image/*">
                </div>
                <?php if(isset($row['picture']) && !empty($row['picture'])): ?>
                <div class="form-group">
                    <label class="form-label">Current Picture:</label>
                    <div>
                        <img src="<?php echo htmlspecialchars($row['picture']); ?>" alt="Current Picture" class="current-image">
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Submit Button -->
            <div class="form-group">
                <button type="submit" class="btn btn-submit" id="submitBtn">
                    <i class="fas fa-save me-2"></i>
                    Save Changes
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Bootstrap 5 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// CNIC input formatting
function formatCNIC(inputElement) {
    inputElement.addEventListener('input', function(event) {
        var value = event.target.value.replace(/[^0-9]/g, '');
        if (value.length > 5) {
            value = value.slice(0, 5) + '-' + value.slice(5);
        }
        if (value.length > 13) {
            value = value.slice(0, 13) + '-' + value.slice(13);
        }
        event.target.value = value;
    });
}

// Apply CNIC formatting to all CNIC fields
['cnic-number', 'father-cnic-no', 'mother-cnic-no', 'spouse-cnic-no'].forEach(function(id) {
    const element = document.getElementById(id);
    if (element) {
        formatCNIC(element);
    }
});

// Form submission with loading state
document.getElementById('editForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.classList.add('loading');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
});
</script>
</body>
</html>
<?php
    } else {
        echo '<div class="container mt-5"><div class="alert alert-danger">No record found with ID: ' . htmlspecialchars($id) . '</div></div>';
    }
} else {
    echo '<div class="container mt-5"><div class="alert alert-danger">ID parameter not set.</div></div>';
}

// Close database connection
$stmt->close();
$conn->close();
?>
