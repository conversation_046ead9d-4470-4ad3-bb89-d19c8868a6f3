<?php include('session.php'); ?>
<?php include 'header.php'; ?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New Verifier - Pakistan Identity Card System</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .header-section .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 10px;
            position: relative;
            z-index: 1;
        }

        .content-section {
            padding: 40px;
        }

        .form-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .form-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 10px;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
            outline: none;
        }

        .input-group-text {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            border-radius: 12px 0 0 12px;
            font-weight: 500;
        }

        .input-group .form-control {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }

        .image-upload-section {
            text-align: center;
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            border: 2px dashed var(--border-color);
            transition: all 0.3s ease;
            position: relative;
        }

        .image-upload-section:hover {
            border-color: var(--primary-color);
            background: rgba(44, 85, 48, 0.02);
        }

        .image-upload-container {
            position: relative;
            display: inline-block;
            cursor: pointer;
        }

        .image-preview {
            max-width: 250px;
            max-height: 250px;
            object-fit: cover;
            border-radius: 15px;
            border: 3px solid var(--primary-color);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .image-preview:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .upload-placeholder {
            width: 250px;
            height: 250px;
            border: 3px dashed var(--border-color);
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: var(--text-dark);
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .upload-placeholder:hover {
            border-color: var(--primary-color);
            background: rgba(44, 85, 48, 0.05);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .image-upload-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(44, 85, 48, 0.8);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            color: white;
            font-size: 1.5rem;
        }

        .image-upload-container:hover .image-upload-overlay {
            opacity: 1;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            border: none;
            color: white;
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: white;
        }

        .action-buttons {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
        }

        .required-indicator {
            color: var(--danger-color);
            font-weight: bold;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-section {
                padding: 20px;
            }

            .header-section h1 {
                font-size: 2rem;
            }

            .content-section {
                padding: 25px;
            }

            .form-card {
                padding: 20px;
            }

            .form-section {
                padding: 20px;
            }

            .image-upload-section {
                padding: 20px;
            }

            .image-preview,
            .upload-placeholder {
                max-width: 200px;
                max-height: 200px;
                width: 200px;
                height: 200px;
            }

            .action-buttons .btn {
                margin-bottom: 10px;
            }
        }

        @media (max-width: 480px) {
            .header-section h1 {
                font-size: 1.8rem;
            }

            .content-section {
                padding: 20px;
            }

            .form-card {
                padding: 15px;
            }

            .form-section {
                padding: 15px;
            }

            .image-preview,
            .upload-placeholder {
                max-width: 180px;
                max-height: 180px;
                width: 180px;
                height: 180px;
            }
        }

        /* Animation */
        .form-card, .form-section, .image-upload-section {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .image-upload-section { animation-delay: 0.1s; }
        .form-section { animation-delay: 0.2s; }
        .action-buttons { animation-delay: 0.3s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Validation styles */
        .is-invalid {
            border-color: var(--danger-color) !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }

        .is-valid {
            border-color: var(--success-color) !important;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
        }
    </style>
</head>
<body>
<div class="main-container">
    <!-- Header Section -->
    <div class="header-section">
        <h1><i class="fas fa-user-plus me-3"></i>Add New Verifier</h1>
        <p class="subtitle">Register a New Pakistan Identity Card Verifier</p>
    </div>

    <!-- Content Section -->
    <div class="content-section">
        <form action="submit_form.php" method="post" enctype="multipart/form-data" id="verifierForm">

            <!-- Image Upload Section -->
            <div class="image-upload-section">
                <h4 class="section-title">
                    <i class="fas fa-camera"></i>
                    Profile Image
                </h4>
                <div class="image-upload-container">
                    <input type="file" name="image" id="imageUpload" class="d-none" accept="image/*" onchange="previewImage(this)">
                    <label for="imageUpload" class="d-block">
                        <div id="uploadPlaceholder" class="upload-placeholder">
                            <div class="upload-icon">
                                <i class="fas fa-camera"></i>
                            </div>
                            <div>Click to Upload Image</div>
                            <small class="text-muted mt-2">JPG, PNG, GIF (Max 5MB)</small>
                        </div>
                        <img id="imagePreview" class="image-preview" style="display: none;" alt="Preview">
                        <div class="image-upload-overlay">
                            <i class="fas fa-edit"></i> Change Image
                        </div>
                    </label>
                </div>
            </div>

            <!-- Personal Information Section -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-user"></i>
                    Personal Information
                </h4>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-user text-primary"></i>
                            Full Name <span class="required-indicator">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="name" name="name"
                                   placeholder="Enter full name" required>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="cnic" class="form-label">
                            <i class="fas fa-id-card text-primary"></i>
                            CNIC Number <span class="required-indicator">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                            <input type="text" class="form-control" id="cnic" name="cnic"
                                   placeholder="00000-0000000-0" required maxlength="15">
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="father_name" class="form-label">
                            <i class="fas fa-male text-primary"></i>
                            Father's Name <span class="required-indicator">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-male"></i></span>
                            <input type="text" class="form-control" id="father_name" name="father_name"
                                   placeholder="Enter father's name" required>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="husband_name" class="form-label">
                            <i class="fas fa-heart text-primary"></i>
                            Husband's Name
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-heart"></i></span>
                            <input type="text" class="form-control" id="husband_name" name="husband_name"
                                   placeholder="Enter husband's name (if applicable)">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-address-book"></i>
                    Contact Information
                </h4>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope text-primary"></i>
                            Email Address <span class="required-indicator">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input type="email" class="form-control" id="email" name="email"
                                   placeholder="<EMAIL>" required>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="phone_number" class="form-label">
                            <i class="fas fa-phone text-primary"></i>
                            Phone Number
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                            <input type="tel" class="form-control" id="phone_number" name="phone_number"
                                   placeholder="+92 300 0000000">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <a href="show_emails.php" class="btn btn-secondary w-100">
                            <i class="fas fa-arrow-left me-2"></i>Cancel & Go Back
                        </a>
                    </div>
                    <div class="col-md-6">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-user-plus me-2"></i>Register Verifier
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Bootstrap 5 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
$(document).ready(function() {
    // Add smooth animations
    const elements = document.querySelectorAll('.image-upload-section, .form-section, .action-buttons');
    elements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        setTimeout(() => {
            el.style.transition = 'all 0.6s ease';
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Form validation
    $('#verifierForm').on('submit', function(e) {
        let isValid = true;
        const requiredFields = ['name', 'email', 'cnic', 'father_name'];

        requiredFields.forEach(function(field) {
            const input = $(`#${field}`);
            if (!input.val().trim()) {
                input.addClass('is-invalid');
                isValid = false;
            } else {
                input.removeClass('is-invalid').addClass('is-valid');
            }
        });

        // Email validation
        const email = $('#email').val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            $('#email').addClass('is-invalid').removeClass('is-valid');
            isValid = false;
        } else if (email) {
            $('#email').removeClass('is-invalid').addClass('is-valid');
        }

        // CNIC validation
        const cnic = $('#cnic').val();
        const cnicRegex = /^\d{5}-\d{7}-\d{1}$/;
        if (cnic && !cnicRegex.test(cnic)) {
            $('#cnic').addClass('is-invalid').removeClass('is-valid');
            isValid = false;
        } else if (cnic) {
            $('#cnic').removeClass('is-invalid').addClass('is-valid');
        }

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields correctly.');
            // Scroll to first invalid field
            $('.is-invalid').first().focus();
        }
    });

    // Real-time validation
    $('#email').on('blur', function() {
        const email = $(this).val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            $(this).addClass('is-invalid').removeClass('is-valid');
        } else if (email) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        }
    });

    // CNIC Auto-formatting and validation
    $('#cnic').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');

        // Limit to 13 digits
        value = value.slice(0, 13);

        // Format with dashes
        if (value.length >= 5) {
            value = value.substring(0, 5) + '-' + value.substring(5);
        }
        if (value.length >= 13) {
            value = value.substring(0, 13) + '-' + value.substring(13, 14);
        }

        $(this).val(value);

        // Validate format
        const cnicRegex = /^\d{5}-\d{7}-\d{1}$/;
        if (value && cnicRegex.test(value)) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else if (value) {
            $(this).addClass('is-invalid').removeClass('is-valid');
        }
    });

    // Phone number formatting
    $('#phone_number').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');

        // Handle Pakistani phone numbers
        if (value.length > 0 && !value.startsWith('92')) {
            if (value.startsWith('0')) {
                value = '92' + value.substring(1);
            } else {
                value = '92' + value;
            }
        }

        // Format as +92 XXX XXXXXXX
        if (value.length > 2) {
            value = '+' + value.substring(0, 2) + ' ' + value.substring(2, 5) + ' ' + value.substring(5);
        }

        $(this).val(value);
    });

    // Real-time validation for required fields
    $('input[required]').on('blur', function() {
        if ($(this).val().trim()) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).addClass('is-invalid').removeClass('is-valid');
        }
    });
});

function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    const placeholder = document.getElementById('uploadPlaceholder');

    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file.');
            input.value = '';
            return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('Image size should be less than 5MB.');
            input.value = '';
            return;
        }

        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
            placeholder.style.display = 'none';

            // Add animation
            preview.style.opacity = '0';
            setTimeout(() => {
                preview.style.transition = 'opacity 0.3s ease';
                preview.style.opacity = '1';
            }, 10);
        }

        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
        placeholder.style.display = 'flex';
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        $('#verifierForm').submit();
    }
    if (e.key === 'Escape') {
        window.location.href = 'show_emails.php';
    }
});

// Form auto-save to localStorage (optional)
function autoSave() {
    const formData = {
        name: $('#name').val(),
        email: $('#email').val(),
        cnic: $('#cnic').val(),
        father_name: $('#father_name').val(),
        husband_name: $('#husband_name').val(),
        phone_number: $('#phone_number').val()
    };
    localStorage.setItem('verifierFormData', JSON.stringify(formData));
}

// Load saved data on page load
function loadSavedData() {
    const savedData = localStorage.getItem('verifierFormData');
    if (savedData) {
        const data = JSON.parse(savedData);
        Object.keys(data).forEach(key => {
            if (data[key]) {
                $(`#${key}`).val(data[key]);
            }
        });
    }
}

// Auto-save every 30 seconds
setInterval(autoSave, 30000);

// Load saved data when page loads
$(document).ready(function() {
    loadSavedData();
});

// Clear saved data on successful submission
$('#verifierForm').on('submit', function() {
    localStorage.removeItem('verifierFormData');
});
</script>
</body>
</html>
