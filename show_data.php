<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include('db_connection.php');
include('session.php');
include('header.php');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Show Data - Token Slip Records</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .header-section .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 10px;
            position: relative;
            z-index: 1;
        }

        .content-section {
            padding: 40px;
        }

        .search-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .search-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
            outline: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            border: none;
            color: white;
            padding: 12px 25px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: white;
        }

        .table-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            overflow: hidden;
            position: relative;
        }

        .scroll-indicator {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: var(--primary-color);
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .scroll-indicator i {
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(0);
            }
            40% {
                transform: translateX(-5px);
            }
            60% {
                transform: translateX(5px);
            }
        }

        .table-responsive {
            border-radius: 12px;
            overflow-x: auto;
            overflow-y: visible;
            max-width: 100%;
            white-space: nowrap;
            position: relative;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) #f1f1f1;
        }

        /* Custom scrollbar for webkit browsers */
        .table-responsive::-webkit-scrollbar {
            height: 8px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        /* Scroll shadow indicators */
        .table-responsive::before,
        .table-responsive::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 20px;
            pointer-events: none;
            z-index: 2;
            transition: opacity 0.3s ease;
        }

        .table-responsive::before {
            left: 0;
            background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
            opacity: 0;
        }

        .table-responsive::after {
            right: 0;
            background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
            opacity: 1;
        }

        .table-responsive.scrolled-left::before {
            opacity: 1;
        }

        .table-responsive.can-scroll-right::after {
            opacity: 1;
        }

        .table-responsive:not(.can-scroll-right)::after {
            opacity: 0;
        }

        .table {
            margin-bottom: 0;
            font-size: 0.85rem;
            min-width: 1800px; /* Ensure minimum width for all columns */
            width: max-content;
        }

        .table th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 12px 8px;
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
            text-align: center;
        }

        .table td {
            padding: 10px 8px;
            vertical-align: middle;
            border-bottom: 1px solid var(--border-color);
            white-space: nowrap;
            text-align: center;
        }

        .table tbody tr:hover {
            background-color: rgba(44, 85, 48, 0.05);
        }

        /* Sticky Actions Column */
        .table th:last-child,
        .table td:last-child {
            position: sticky;
            right: 0;
            background: white;
            z-index: 5;
            border-left: 2px solid var(--border-color);
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
        }

        .table th:last-child {
            z-index: 11;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 3px;
            min-width: 160px;
            max-width: 160px;
            padding: 5px;
        }

        .action-btn {
            padding: 4px 8px;
            font-size: 0.7rem;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            white-space: nowrap;
            display: block;
            width: 100%;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .btn-edit {
            background: #007bff;
            color: white;
        }

        .btn-print {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: #212529;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .status-btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .status-submitted {
            background: #d4edda;
            color: #155724;
        }

        .status-unsubmitted {
            background: #f8d7da;
            color: #721c24;
        }

        .unsubmitted {
            background-color: rgba(248, 215, 218, 0.3) !important;
        }

        .small-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .small-image:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        /* Image hover preview */
        .image-preview {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            z-index: 9999;
            background: white;
            padding: 15px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            display: none;
            max-width: 90vw;
            max-height: 90vh;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .image-preview img {
            max-width: 400px;
            max-height: 400px;
            object-fit: contain;
            border-radius: 10px;
            display: block;
        }

        .image-preview .close-btn {
            position: absolute;
            top: -10px;
            right: -10px;
            background: var(--danger-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .image-preview .image-info {
            text-align: center;
            margin-top: 10px;
            color: var(--text-dark);
            font-size: 0.9rem;
        }

        /* Overlay for image preview */
        .image-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 9998;
            display: none;
            backdrop-filter: blur(5px);
            opacity: 0;
            transition: opacity 0.3s ease;
            cursor: pointer;
        }

        .pagination {
            justify-content: center;
            margin-top: 30px;
        }

        .page-link {
            color: var(--primary-color);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            margin: 0 2px;
            padding: 10px 15px;
            font-weight: 500;
        }

        .page-link:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-section {
                padding: 20px;
            }

            .header-section h1 {
                font-size: 2rem;
            }

            .content-section {
                padding: 25px;
            }

            .search-card {
                padding: 20px;
            }

            .table-card {
                padding: 15px;
            }

            .table {
                font-size: 0.75rem;
                min-width: 1600px; /* Reduced for mobile but still wide enough */
            }

            .table th,
            .table td {
                padding: 8px 6px;
            }

            .action-buttons {
                min-width: 140px;
                max-width: 140px;
            }

            .action-btn {
                font-size: 0.65rem;
                padding: 3px 6px;
            }

            .scroll-indicator {
                font-size: 0.8rem;
                padding: 8px 12px;
            }
        }

        @media (max-width: 480px) {
            .header-section h1 {
                font-size: 1.8rem;
            }

            .content-section {
                padding: 20px;
            }

            .search-card {
                padding: 15px;
            }
        }

        /* Animation */
        .search-card, .table-card {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .search-card { animation-delay: 0.1s; }
        .table-card { animation-delay: 0.2s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
<div class="main-container">
    <!-- Header Section -->
    <div class="header-section">
        <h1><i class="fas fa-table me-3"></i>Token Slip Records</h1>
        <p class="subtitle">View and Manage Pakistan Identity Card Applications</p>
    </div>

    <!-- Content Section -->
    <div class="content-section">
        <!-- Search Section -->
        <div class="search-card">
            <h3 class="search-title">
                <i class="fas fa-search"></i>
                Search & Filter Records
            </h3>
            <form method="GET" id="searchForm">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label for="search" class="form-label">
                                <i class="fas fa-search me-2"></i>Search
                            </label>
                            <input type="text" class="form-control" id="search" name="search"
                                   placeholder="Search by name, CNIC, tracking ID..."
                                   value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-3">
                            <label for="records_per_page" class="form-label">
                                <i class="fas fa-list me-2"></i>Records per page
                            </label>
                            <select class="form-select" id="records_per_page" name="limit">
                                <option value="10" <?php echo (isset($_GET['limit']) && $_GET['limit'] == '10') ? 'selected' : ''; ?>>10</option>
                                <option value="25" <?php echo (isset($_GET['limit']) && $_GET['limit'] == '25') ? 'selected' : ''; ?>>25</option>
                                <option value="50" <?php echo (isset($_GET['limit']) && $_GET['limit'] == '50') ? 'selected' : ''; ?>>50</option>
                                <option value="100" <?php echo (isset($_GET['limit']) && $_GET['limit'] == '100') ? 'selected' : ''; ?>>100</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="from_date" class="form-label">
                                <i class="fas fa-calendar-alt me-2"></i>From Date
                            </label>
                            <input type="date" class="form-control" name="from_date"
                                   value="<?php echo isset($_GET['from_date']) ? htmlspecialchars($_GET['from_date']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="to_date" class="form-label">
                                <i class="fas fa-calendar-alt me-2"></i>To Date
                            </label>
                            <input type="date" class="form-control" name="to_date"
                                   value="<?php echo isset($_GET['to_date']) ? htmlspecialchars($_GET['to_date']) : ''; ?>">
                        </div>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                    <?php if(isset($_GET['search']) || isset($_GET['from_date']) || isset($_GET['to_date'])): ?>
                        <a href="show_data.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Clear
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <!-- Data Table -->
        <div class="table-card">
            <div class="scroll-indicator">
                <i class="fas fa-arrows-alt-h"></i>
                <span><strong>Tip:</strong> Scroll horizontally to view all columns. The Actions column stays fixed on the right.</span>
            </div>
            <div class="table-responsive" id="tableContainer">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 60px; min-width: 60px;">#</th>
                            <th style="width: 80px; min-width: 80px;">Picture</th>
                            <th style="width: 140px; min-width: 140px;">Tracking ID</th>
                            <th style="width: 180px; min-width: 180px;">Name</th>
                            <th style="width: 140px; min-width: 140px;">CNIC Number</th>
                            <th style="width: 160px; min-width: 160px;">Application Type</th>
                            <th style="width: 120px; min-width: 120px;">Priority</th>
                            <th style="width: 110px; min-width: 110px;">Date</th>
                            <th style="width: 90px; min-width: 90px;">Time</th>
                            <th style="width: 140px; min-width: 140px;">Mobile Number</th>
                            <th style="width: 180px; min-width: 180px;">Email ID</th>
                            <th style="width: 180px; min-width: 180px;">Father Name</th>
                            <th style="width: 140px; min-width: 140px;">Father CNIC</th>
                            <th style="width: 180px; min-width: 180px;">Mother Name</th>
                            <th style="width: 140px; min-width: 140px;">Mother CNIC</th>
                            <th style="width: 180px; min-width: 180px;">Spouse Name</th>
                            <th style="width: 140px; min-width: 140px;">Spouse CNIC</th>
                            <th style="width: 130px; min-width: 130px;">Date of Birth</th>
                            <th style="width: 220px; min-width: 220px;">Present Address</th>
                            <th style="width: 220px; min-width: 220px;">Permanent Address</th>
                            <th style="width: 120px; min-width: 120px;">Status</th>
                            <th style="width: 180px; min-width: 180px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Pagination
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
                        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                        $offset = ($page - 1) * $limit;

                        // Base query
                        $sql = "SELECT * FROM tokenslip WHERE 1=1";
                        $params = array();
                        $types = "";

                        // Search conditions
                        if (!empty($_GET['search'])) {
                            $search = '%' . $_GET['search'] . '%';
                            $sql .= " AND (tracking_id LIKE ? OR name LIKE ? OR father_name LIKE ? OR cnic_number LIKE ? OR mobile_number LIKE ? OR email_id LIKE ?)";
                            $params[] = $search;
                            $params[] = $search;
                            $params[] = $search;
                            $params[] = $search;
                            $params[] = $search;
                            $params[] = $search;
                            $types .= "ssssss";
                        }

                        // Date range conditions
                        if (!empty($_GET['from_date'])) {
                            $sql .= " AND date >= ?";
                            $params[] = $_GET['from_date'];
                            $types .= "s";
                        }
                        if (!empty($_GET['to_date'])) {
                            $sql .= " AND date <= ?";
                            $params[] = $_GET['to_date'];
                            $types .= "s";
                        }

                        // Get total records for pagination
                        $stmt = $conn->prepare($sql);
                        if (!empty($params)) {
                            $stmt->bind_param($types, ...$params);
                        }
                        $stmt->execute();
                        $total_result = $stmt->get_result();
                        $total_records = $total_result->num_rows;
                        $total_pages = ceil($total_records / $limit);

                        // Add ORDER BY and LIMIT to the main query
                        $sql .= " ORDER BY date DESC, time DESC, id DESC LIMIT ? OFFSET ?";
                        $params[] = $limit;
                        $params[] = $offset;
                        $types .= "ii";

                        $stmt = $conn->prepare($sql);
                        if (!empty($params)) {
                            $stmt->bind_param($types, ...$params);
                        }
                        $stmt->execute();
                        $result = $stmt->get_result();

                        if ($result->num_rows > 0) {
                            $counter = $offset + 1;
                            while ($row = $result->fetch_assoc()) {
                                $statusClass = $row["status"] == 'Unsubmitted' ? 'unsubmitted' : '';
                                echo "<tr class='" . $statusClass . "'>";
                                echo "<td>" . $counter++ . "</td>";
                                echo "<td>";
                                if (isset($row['image_path']) && !empty($row['image_path'])) {
                                    // Remove duplicate 'uploads/' from path if it exists
                                    $imageUrl = $row['image_path'];
                                    if (strpos($imageUrl, 'uploads/') !== 0) {
                                        $imageUrl = 'uploads/' . $imageUrl;
                                    }
                                    echo "<img src='" . $imageUrl . "' class='small-image preview-image' alt='User Image' data-name='" . htmlspecialchars($row['name']) . "' data-cnic='" . htmlspecialchars($row['cnic_number']) . "'>";
                                } else {
                                    echo "<div class='small-image d-flex align-items-center justify-content-center bg-light'>";
                                    echo "<i class='fas fa-user text-muted'></i>";
                                    echo "</div>";
                                }
                                echo "</td>";
                                echo "<td>" . htmlspecialchars($row['tracking_id']) . "</td>";
                                echo "<td class='urdu-text'>" . htmlspecialchars($row['name']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['cnic_number']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['application_type']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['priority']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['date']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['time']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['mobile_number']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['email_id']) . "</td>";
                                echo "<td class='urdu-text'>" . htmlspecialchars($row['father_name']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['father_cnic_number']) . "</td>";
                                echo "<td class='urdu-text'>" . htmlspecialchars($row['mother_name']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['mother_cnic_number']) . "</td>";
                                echo "<td class='urdu-text'>" . htmlspecialchars($row['spouse_name']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['spouse_cnic_number']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['date_of_birth']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['present_address']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['permanent_address']) . "</td>";

                                // Status button with better styling
                                $statusClass = $row['status'] == 'Submitted' ? 'status-submitted' : 'status-unsubmitted';
                                echo "<td><button class='status-btn $statusClass' data-id='" . $row['id'] . "' data-status='" . $row['status'] . "'>" . $row['status'] . "</button></td>";

                                // Action buttons with improved styling
                                echo "<td class='action-buttons' style='position: sticky; right: 0; background: white;'>";
                                echo "<a href='edit.php?id=" . $row['id'] . "' class='action-btn btn-edit'><i class='fas fa-edit'></i> Edit</a>";
                                echo "<a href='print.php?id=" . $row['id'] . "' class='action-btn btn-print'><i class='fas fa-print'></i> Print</a>";
                                echo "<a href='print_bayan_halfi.php?id=" . $row['id'] . "' target='_blank' class='action-btn btn-warning'><i class='fas fa-file-alt'></i> بیان حلفی</a>";
                                echo "<button class='action-btn btn-info print-token' data-cnic='" . htmlspecialchars($row['cnic_number']) . "'>";
                                echo "<i class='fas fa-print'></i> Token";
                                echo "</button>";
                                echo "<button onclick='deleteRecord(" . $row['id'] . ")' class='action-btn btn-danger'><i class='fas fa-trash'></i> Delete</button>";
                                echo "</td>";
                                echo "</tr>";
                            }
                        } else {
                            echo "<tr><td colspan='22' class='text-center py-5'>";
                            echo "<i class='fas fa-inbox fa-3x text-muted mb-3'></i><br>";
                            echo "<h5 class='text-muted'>No records found</h5>";
                            echo "<p class='text-muted'>Try adjusting your search criteria</p>";
                            echo "</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=1<?php echo isset($_GET['search']) ? '&search='.urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['from_date']) ? '&from_date='.urlencode($_GET['from_date']) : ''; ?><?php echo isset($_GET['to_date']) ? '&to_date='.urlencode($_GET['to_date']) : ''; ?><?php echo isset($_GET['limit']) ? '&limit='.urlencode($_GET['limit']) : ''; ?>">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                <?php endif; ?>

                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $page == $i ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo isset($_GET['search']) ? '&search='.urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['from_date']) ? '&from_date='.urlencode($_GET['from_date']) : ''; ?><?php echo isset($_GET['to_date']) ? '&to_date='.urlencode($_GET['to_date']) : ''; ?><?php echo isset($_GET['limit']) ? '&limit='.urlencode($_GET['limit']) : ''; ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $total_pages; ?><?php echo isset($_GET['search']) ? '&search='.urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['from_date']) ? '&from_date='.urlencode($_GET['from_date']) : ''; ?><?php echo isset($_GET['to_date']) ? '&to_date='.urlencode($_GET['to_date']) : ''; ?><?php echo isset($_GET['limit']) ? '&limit='.urlencode($_GET['limit']) : ''; ?>">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>
</div>

<!-- Image Preview Modal -->
<div class="image-overlay" id="imageOverlay"></div>
<div class="image-preview" id="imagePreview">
    <button class="close-btn" onclick="closeImagePreview()">
        <i class="fas fa-times"></i>
    </button>
    <img id="previewImage" src="" alt="Preview">
    <div class="image-info" id="imageInfo"></div>
</div>

<!-- Bootstrap 5 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
$(document).ready(function() {
    // Live search functionality
    let searchTimer;
    $('#search').on('input', function() {
        clearTimeout(searchTimer);
        searchTimer = setTimeout(function() {
            $('#searchForm').submit();
        }, 500);
    });

    // Records per page change handler
    $('#records_per_page').change(function() {
        $('#searchForm').submit();
    });

    // Status button click handler
    $('.status-btn').click(function() {
        var button = $(this);
        var id = button.data('id');
        var status = button.data('status') === 'Unsubmitted' ? 'Submitted' : 'Unsubmitted';

        // Add loading state
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');

        $.ajax({
            url: 'update_status.php',
            type: 'POST',
            data: { id: id, status: status },
            success: function(response) {
                button.data('status', status).text(status);
                button.closest('tr').toggleClass('unsubmitted', status === 'Unsubmitted');

                // Update button class
                button.removeClass('status-submitted status-unsubmitted');
                button.addClass(status === 'Submitted' ? 'status-submitted' : 'status-unsubmitted');

                button.prop('disabled', false);
            },
            error: function() {
                alert('Error updating status. Please try again.');
                button.prop('disabled', false).text(button.data('status'));
            }
        });
    });

    // Print Token functionality
    $('.print-token').on('click', function() {
        var cnic = $(this).data('cnic');
        // Open slip.php with the CNIC pre-populated
        window.open('slip.php?cnic=' + encodeURIComponent(cnic), '_blank');
    });

    // Add hover effects to action buttons
    $('.action-btn').hover(
        function() { $(this).addClass('shadow-sm'); },
        function() { $(this).removeClass('shadow-sm'); }
    );

    // Image preview functionality
    $('.preview-image').on('click', function() {
        const imageSrc = $(this).attr('src');
        const userName = $(this).data('name');
        const userCnic = $(this).data('cnic');

        showImagePreview(imageSrc, userName, userCnic);
    });

    // Table scroll enhancement
    const tableContainer = document.getElementById('tableContainer');
    if (tableContainer) {
        // Add scroll shadow indicators
        function updateScrollShadows() {
            const scrollLeft = tableContainer.scrollLeft;
            const scrollWidth = tableContainer.scrollWidth;
            const clientWidth = tableContainer.clientWidth;

            // Add/remove classes based on scroll position
            if (scrollLeft > 0) {
                tableContainer.classList.add('scrolled-left');
            } else {
                tableContainer.classList.remove('scrolled-left');
            }

            if (scrollLeft < scrollWidth - clientWidth - 1) {
                tableContainer.classList.add('can-scroll-right');
            } else {
                tableContainer.classList.remove('can-scroll-right');
            }
        }

        tableContainer.addEventListener('scroll', updateScrollShadows);
        updateScrollShadows(); // Initial check

        // Smooth scrolling with mouse wheel
        tableContainer.addEventListener('wheel', function(e) {
            if (e.deltaY !== 0) {
                e.preventDefault();
                tableContainer.scrollLeft += e.deltaY;
            }
        });
    }
});

function deleteRecord(id) {
    if (confirm('Are you sure you want to delete this record? This action cannot be undone.')) {
        window.location.href = 'delete.php?id=' + id;
    }
}

// Image preview functions
function showImagePreview(imageSrc, userName, userCnic) {
    const overlay = document.getElementById('imageOverlay');
    const preview = document.getElementById('imagePreview');
    const previewImage = document.getElementById('previewImage');
    const imageInfo = document.getElementById('imageInfo');

    // Set image source and info
    previewImage.src = imageSrc;
    imageInfo.innerHTML = `
        <strong>${userName}</strong><br>
        <small>CNIC: ${userCnic}</small>
    `;

    // Show overlay and preview
    overlay.style.display = 'block';
    preview.style.display = 'block';

    // Add animation
    setTimeout(() => {
        overlay.style.opacity = '1';
        preview.style.opacity = '1';
        preview.style.transform = 'translate(-50%, -50%) scale(1)';
    }, 10);

    // Close on overlay click
    overlay.onclick = closeImagePreview;

    // Close on Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeImagePreview();
        }
    });
}

function closeImagePreview() {
    const overlay = document.getElementById('imageOverlay');
    const preview = document.getElementById('imagePreview');

    // Add closing animation
    overlay.style.opacity = '0';
    preview.style.opacity = '0';
    preview.style.transform = 'translate(-50%, -50%) scale(0.8)';

    // Hide after animation
    setTimeout(() => {
        overlay.style.display = 'none';
        preview.style.display = 'none';
        preview.style.transform = 'translate(-50%, -50%) scale(1)';
    }, 300);
}
</script>
</body>
</html>
<?php include 'footer.php'; ?>
<?php $conn->close(); ?>
