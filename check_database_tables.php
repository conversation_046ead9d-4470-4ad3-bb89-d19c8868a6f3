<?php
// Check all tables in the database and find login-related tables
include 'db_connection.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Database Tables Check</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} table{border-collapse:collapse;width:100%;margin:10px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background-color:#f2f2f2;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>Database Tables Analysis</h1>";
echo "<p><strong>Database:</strong> u904712216_pakidentity</p>";

// Show all tables
echo "<h2>All Tables in Database:</h2>";
$result = $conn->query("SHOW TABLES");
if ($result->num_rows > 0) {
    echo "<table>";
    echo "<tr><th>Table Name</th><th>Action</th></tr>";
    while ($row = $result->fetch_array()) {
        $table_name = $row[0];
        echo "<tr>";
        echo "<td>" . $table_name . "</td>";
        echo "<td><a href='?check_table=" . $table_name . "'>Check Structure</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='error'>No tables found in database!</p>";
}

// Check specific table if requested
if (isset($_GET['check_table'])) {
    $table_name = $_GET['check_table'];
    echo "<h2>Table Structure: " . htmlspecialchars($table_name) . "</h2>";
    
    // Show table structure
    $result = $conn->query("DESCRIBE " . $table_name);
    if ($result) {
        echo "<table>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // If it looks like a login table, show data
        if (strpos(strtolower($table_name), 'login') !== false || strpos(strtolower($table_name), 'user') !== false || strpos(strtolower($table_name), 'admin') !== false) {
            echo "<h3>Data in " . htmlspecialchars($table_name) . ":</h3>";
            $data_result = $conn->query("SELECT * FROM " . $table_name . " LIMIT 10");
            if ($data_result && $data_result->num_rows > 0) {
                echo "<table>";
                // Get column names
                $fields = $data_result->fetch_fields();
                echo "<tr>";
                foreach ($fields as $field) {
                    echo "<th>" . $field->name . "</th>";
                }
                echo "</tr>";
                
                // Show data
                while ($row = $data_result->fetch_assoc()) {
                    echo "<tr>";
                    foreach ($row as $key => $value) {
                        if ($key == 'password') {
                            echo "<td>[HIDDEN]</td>";
                        } else {
                            echo "<td>" . htmlspecialchars($value) . "</td>";
                        }
                    }
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='info'>No data found in this table.</p>";
            }
        }
    }
}

// Search for login-related tables
echo "<h2>Login-Related Tables Search:</h2>";
$login_tables = [];
$result = $conn->query("SHOW TABLES");
while ($row = $result->fetch_array()) {
    $table_name = strtolower($row[0]);
    if (strpos($table_name, 'login') !== false || 
        strpos($table_name, 'user') !== false || 
        strpos($table_name, 'admin') !== false || 
        strpos($table_name, 'auth') !== false) {
        $login_tables[] = $row[0];
    }
}

if (!empty($login_tables)) {
    echo "<p class='success'>Found potential login tables:</p>";
    echo "<ul>";
    foreach ($login_tables as $table) {
        echo "<li><a href='?check_table=" . $table . "'>" . $table . "</a></li>";
    }
    echo "</ul>";
} else {
    echo "<p class='error'>No login-related tables found!</p>";
}

// Check current session
echo "<h2>Current Session Information:</h2>";
session_start();
if (isset($_SESSION['user_id'])) {
    echo "<p><strong>Session User ID:</strong> " . $_SESSION['user_id'] . "</p>";
    echo "<p><strong>Session Username:</strong> " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'Not set') . "</p>";
    
    // Try to find this user in different tables
    echo "<h3>Searching for User ID " . $_SESSION['user_id'] . " in all potential login tables:</h3>";
    foreach ($login_tables as $table) {
        try {
            $stmt = $conn->prepare("SELECT * FROM " . $table . " WHERE id = ?");
            $stmt->bind_param("i", $_SESSION['user_id']);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $user = $result->fetch_assoc();
                echo "<p class='success'>✅ User found in table: <strong>" . $table . "</strong></p>";
                echo "<ul>";
                foreach ($user as $key => $value) {
                    if ($key != 'password') {
                        echo "<li><strong>" . $key . ":</strong> " . htmlspecialchars($value) . "</li>";
                    }
                }
                echo "</ul>";
            } else {
                echo "<p class='error'>❌ User not found in table: " . $table . "</p>";
            }
            $stmt->close();
        } catch (Exception $e) {
            echo "<p class='error'>Error checking table " . $table . ": " . $e->getMessage() . "</p>";
        }
    }
} else {
    echo "<p class='info'>No active session found.</p>";
}

echo "<h2>Quick Actions:</h2>";
echo "<ul>";
echo "<li><a href='login.php'>Go to Login</a></li>";
echo "<li><a href='setup_admin.php'>Setup Admin User</a></li>";
echo "<li><a href='logout.php'>Logout</a></li>";
echo "</ul>";

$conn->close();
echo "</body></html>";
?>
