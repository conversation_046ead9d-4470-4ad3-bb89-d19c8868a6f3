<?php 
include('session.php'); 
include 'header.php'; 
include_once 'db_connection.php';

// Handle form submissions
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_app_type':
                    $urdu_name = trim($_POST['urdu_name']);
                    $english_name = trim($_POST['english_name']);
                    
                    if (empty($urdu_name) || empty($english_name)) {
                        throw new Exception("Both Urdu and English names are required");
                    }
                    
                    $stmt = $conn->prepare("INSERT INTO application_types (urdu_name, english_name) VALUES (?, ?)");
                    $stmt->bind_param("ss", $urdu_name, $english_name);
                    
                    if ($stmt->execute()) {
                        $message = "Application type added successfully!";
                        $message_type = "success";
                    } else {
                        throw new Exception("Error adding application type");
                    }
                    $stmt->close();
                    break;
                    
                case 'add_priority':
                    $urdu_name = trim($_POST['urdu_name']);
                    $english_name = trim($_POST['english_name']);
                    
                    if (empty($urdu_name) || empty($english_name)) {
                        throw new Exception("Both Urdu and English names are required");
                    }
                    
                    $stmt = $conn->prepare("INSERT INTO priorities (urdu_name, english_name) VALUES (?, ?)");
                    $stmt->bind_param("ss", $urdu_name, $english_name);
                    
                    if ($stmt->execute()) {
                        $message = "Priority added successfully!";
                        $message_type = "success";
                    } else {
                        throw new Exception("Error adding priority");
                    }
                    $stmt->close();
                    break;
                    
                case 'toggle_app_type':
                    $id = intval($_POST['id']);
                    $is_active = intval($_POST['is_active']);
                    
                    $stmt = $conn->prepare("UPDATE application_types SET is_active = ? WHERE id = ?");
                    $stmt->bind_param("ii", $is_active, $id);
                    
                    if ($stmt->execute()) {
                        $message = "Application type status updated!";
                        $message_type = "success";
                    } else {
                        throw new Exception("Error updating application type status");
                    }
                    $stmt->close();
                    break;
                    
                case 'toggle_priority':
                    $id = intval($_POST['id']);
                    $is_active = intval($_POST['is_active']);
                    
                    $stmt = $conn->prepare("UPDATE priorities SET is_active = ? WHERE id = ?");
                    $stmt->bind_param("ii", $is_active, $id);
                    
                    if ($stmt->execute()) {
                        $message = "Priority status updated!";
                        $message_type = "success";
                    } else {
                        throw new Exception("Error updating priority status");
                    }
                    $stmt->close();
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $message_type = "error";
    }
}

// Fetch current data
$application_types = [];
$priorities = [];

try {
    // Fetch application types
    $result = $conn->query("SELECT * FROM application_types ORDER BY english_name");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $application_types[] = $row;
        }
    }
    
    // Fetch priorities
    $result = $conn->query("SELECT * FROM priorities ORDER BY id");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $priorities[] = $row;
        }
    }
} catch (Exception $e) {
    $message = "Error fetching data: " . $e->getMessage();
    $message_type = "error";
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Lookup Data</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content-section {
            padding: 40px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-bottom: none;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        
        .btn-toggle {
            padding: 5px 15px;
            font-size: 0.875rem;
            border-radius: 20px;
        }
        
        .status-active {
            background: #28a745;
            color: white;
        }
        
        .status-inactive {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1><i class="fas fa-cogs me-3"></i>Manage Lookup Data</h1>
            <p class="mb-0">Manage Application Types and Priorities</p>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Application Types Section -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>Application Types</h5>
                        </div>
                        <div class="card-body">
                            <!-- Add New Application Type Form -->
                            <form method="POST" class="mb-4">
                                <input type="hidden" name="action" value="add_app_type">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Urdu Name</label>
                                            <input type="text" class="form-control" name="urdu_name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">English Name</label>
                                            <input type="text" class="form-control" name="english_name" required>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Add Application Type
                                </button>
                            </form>

                            <!-- Application Types List -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Urdu Name</th>
                                            <th>English Name</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($application_types as $app_type): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($app_type['urdu_name']); ?></td>
                                            <td><?php echo htmlspecialchars($app_type['english_name']); ?></td>
                                            <td>
                                                <span class="badge <?php echo $app_type['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $app_type['is_active'] ? 'Active' : 'Inactive'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="toggle_app_type">
                                                    <input type="hidden" name="id" value="<?php echo $app_type['id']; ?>">
                                                    <input type="hidden" name="is_active" value="<?php echo $app_type['is_active'] ? 0 : 1; ?>">
                                                    <button type="submit" class="btn btn-sm <?php echo $app_type['is_active'] ? 'btn-warning' : 'btn-success'; ?>">
                                                        <?php echo $app_type['is_active'] ? 'Deactivate' : 'Activate'; ?>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Priorities Section -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-star me-2"></i>Priorities</h5>
                        </div>
                        <div class="card-body">
                            <!-- Add New Priority Form -->
                            <form method="POST" class="mb-4">
                                <input type="hidden" name="action" value="add_priority">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Urdu Name</label>
                                            <input type="text" class="form-control" name="urdu_name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">English Name</label>
                                            <input type="text" class="form-control" name="english_name" required>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Add Priority
                                </button>
                            </form>

                            <!-- Priorities List -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Urdu Name</th>
                                            <th>English Name</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($priorities as $priority): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($priority['urdu_name']); ?></td>
                                            <td><?php echo htmlspecialchars($priority['english_name']); ?></td>
                                            <td>
                                                <span class="badge <?php echo $priority['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $priority['is_active'] ? 'Active' : 'Inactive'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="toggle_priority">
                                                    <input type="hidden" name="id" value="<?php echo $priority['id']; ?>">
                                                    <input type="hidden" name="is_active" value="<?php echo $priority['is_active'] ? 0 : 1; ?>">
                                                    <button type="submit" class="btn btn-sm <?php echo $priority['is_active'] ? 'btn-warning' : 'btn-success'; ?>">
                                                        <?php echo $priority['is_active'] ? 'Deactivate' : 'Activate'; ?>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="fee.php" class="btn btn-success me-3">
                    <i class="fas fa-money-bill-wave me-2"></i>Manage Fees
                </a>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-home me-2"></i>Home Page
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
