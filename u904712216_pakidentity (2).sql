-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 27, 2025 at 07:18 AM
-- Server version: 10.4.28-MariaDB
-- PHP Version: 8.2.4

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `u372178158_PAKID`
--

-- --------------------------------------------------------

--
-- Table structure for table `application_types`
--

CREATE TABLE `application_types` (
  `id` int(11) NOT NULL,
  `urdu_name` varchar(100) NOT NULL,
  `english_name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `application_types`
--

INSERT INTO `application_types` (`id`, `urdu_name`, `english_name`, `is_active`, `created_at`) VALUES
(1, 'شناختی کارڈ میں ترمیم', 'ID Card Modification', 1, '2025-06-24 05:01:07'),
(2, 'رینو', 'Renewal', 1, '2025-06-24 05:01:07'),
(3, 'گمشدہ', 'Lost Card', 1, '2025-06-24 05:01:07'),
(4, 'فیملی سرٹیفکیٹ', 'Family Certificate', 1, '2025-06-24 05:01:07'),
(5, 'سمارٹ شناختی کارڈ', 'Smart ID Card', 1, '2025-06-24 05:01:07'),
(6, 'شناختی کارڈمنسوخ', 'ID Card Cancellation', 1, '2025-06-24 05:01:07'),
(7, 'حصول نیو شناختی کارڈ', 'New Cnic', 1, '2025-06-24 05:03:04'),
(8, 'حصول بے فارم', 'Bform', 1, '2025-06-24 05:03:34');

-- --------------------------------------------------------

--
-- Table structure for table `emails`
--

CREATE TABLE `emails` (
  `id` int(11) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  `verifier_cnic` varchar(15) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `father_name` varchar(100) DEFAULT NULL,
  `husband_name` varchar(100) DEFAULT NULL,
  `phone_number` varchar(15) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `user_id` int(11) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `emails`
--

INSERT INTO `emails` (`id`, `email`, `image_path`, `verifier_cnic`, `name`, `father_name`, `husband_name`, `phone_number`, `created_at`, `user_id`, `status`) VALUES
(8, '<EMAIL>', NULL, '31103-3680840-9', 'فقیر حسین	', 'احسان الحق	', '', '', '2025-02-05 09:52:43', NULL, 'active'),
(9, '<EMAIL>', NULL, '42501-4396825-5', 'طارق', 'رمضان	', '', '', '2025-02-05 09:52:43', NULL, 'active'),

-- --------------------------------------------------------

--
-- Table structure for table `email_list`
--

CREATE TABLE `email_list` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `email_list`
--

INSERT INTO `email_list` (`id`, `email`, `status`, `created_at`) VALUES
(1, '<EMAIL>', 1, '2025-02-03 04:03:08')

-- --------------------------------------------------------

--
-- Table structure for table `fee`
--

CREATE TABLE `fee` (
  `id` int(11) NOT NULL,
  `application_type` varchar(255) NOT NULL,
  `priority` varchar(50) NOT NULL,
  `fee` int(11) NOT NULL,
  `service_fee` int(11) NOT NULL,
  `total_fee` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `fee`
--

INSERT INTO `fee` (`id`, `application_type`, `priority`, `fee`, `service_fee`, `total_fee`) VALUES
(37, 'شناختی کارڈ میں ترمیم', 'نارمل', 915, 1085, 2000),
(38, 'شناختی کارڈ میں ترمیم', 'ارجنٹ', 1665, 1335, 3000),
(39, 'شناختی کارڈ میں ترمیم', 'ایگزیکٹیو', 2665, 1335, 4000),
(40, 'رینو', 'نارمل', 915, 1085, 2000),
(41, 'رینو', 'ارجنٹ', 1665, 1335, 3000),
(42, 'رینو', 'ایگزیکٹیو', 2665, 1335, 4000),
(43, 'گمشدہ', 'نارمل', 915, 1085, 2000),
(44, 'گمشدہ', 'ارجنٹ', 1665, 1335, 3000),
(45, 'گمشدہ', 'ایگزیکٹیو', 2665, 1335, 4000),
(46, 'فیملی سرٹیفکیٹ', 'نارمل', 1000, 1000, 2000),
(47, 'فیملی سرٹیفکیٹ', 'ارجنٹ', 1000, 1000, 2000),
(48, 'فیملی سرٹیفکیٹ', 'ایگزیکٹیو', 1000, 1000, 2000),
(49, 'سمارٹ شناختی کارڈ', 'نارمل', 915, 1085, 2000),
(50, 'سمارٹ شناختی کارڈ', 'ارجنٹ', 1665, 1335, 3000),
(51, 'سمارٹ شناختی کارڈ', 'ایگزیکٹیو', 2665, 1335, 4000),
(52, 'شناختی کارڈمنسوخ', 'نارمل', 0, 1000, 1000),
(53, 'شناختی کارڈمنسوخ', 'ارجنٹ', 0, 1000, 1000),
(54, 'شناختی کارڈمنسوخ', 'ایگزیکٹیو', 0, 1000, 1000),
(0, 'حصول بے فارم', 'نارمل', 50, 950, 1000);

-- --------------------------------------------------------

--
-- Table structure for table `login`
--

CREATE TABLE `login` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `role` enum('admin','user') DEFAULT 'user',
  `full_name` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `login`
--

INSERT INTO `login` (`id`, `username`, `password`, `created_at`, `role`, `full_name`, `email`, `phone`, `status`, `created_by`, `updated_at`, `last_login`) VALUES
(0, 'admin', '$2y$10$VpK3ttFcbckUVZimfH4F.egGjtrCh0x6psQARgKFBZe8MhDfi/wt2', '2025-06-24 08:38:22', 'admin', 'Administrator', NULL, NULL, 'active', NULL, '2025-06-27 04:51:11', '2025-06-27 04:51:11'),
(0, 'user1', '$2y$10$M.jPM3qIerOn28uFtCm2neM.xeGfllzEIy5Eg.pcxBZajaxK.Wa6u', '2025-06-24 08:38:24', 'user', 'Regular User', NULL, NULL, 'active', NULL, '2025-06-27 04:51:11', '2025-06-27 04:51:11');

-- --------------------------------------------------------

--
-- Table structure for table `login_logs`
--

CREATE TABLE `login_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `login_time` datetime NOT NULL,
  `ip_address` varchar(45) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `login_logs`
--

INSERT INTO `login_logs` (`id`, `user_id`, `login_time`, `ip_address`) VALUES
(1, 1, '2025-01-20 05:07:06', '**************'),


-- --------------------------------------------------------

--
-- Table structure for table `logout_logs`
--

CREATE TABLE `logout_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `logout_time` datetime NOT NULL,
  `ip_address` varchar(45) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `logout_logs`
--

INSERT INTO `logout_logs` (`id`, `user_id`, `logout_time`, `ip_address`) VALUES
(1, 1, '2025-02-02 13:38:21', '::1'),
(2, 1, '2025-02-02 13:38:26', '::1'),
(0, 1, '2025-03-19 07:25:37', '2404:3100:1ca3:3982:b94c:7a99:1f15:1a6e'),
(0, 1, '2025-04-14 06:50:46', '**************'),
(0, 1, '2025-04-14 07:16:24', '2404:3100:1c77:d0eb:d5da:4c8e:4db6:6fe9'),
(0, 1, '2025-06-24 08:03:38', '***************'),
(0, 1, '2025-06-24 08:09:00', '::1');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `used` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `priorities`
--

CREATE TABLE `priorities` (
  `id` int(11) NOT NULL,
  `urdu_name` varchar(50) NOT NULL,
  `english_name` varchar(50) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `priorities`
--

INSERT INTO `priorities` (`id`, `urdu_name`, `english_name`, `is_active`, `created_at`) VALUES
(1, 'نارمل', 'Normal', 1, '2025-06-24 05:01:07'),
(2, 'ارجنٹ', 'Urgent', 1, '2025-06-24 05:01:07'),
(3, 'ایگزیکٹیو', 'Executive', 1, '2025-06-24 05:01:07');

-- --------------------------------------------------------

--
-- Table structure for table `tokenslip`
--

CREATE TABLE `tokenslip` (
  `id` int(11) NOT NULL,
  `tracking_id` varchar(50) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `cnic_number` varchar(15) DEFAULT NULL,
  `application_type` varchar(50) DEFAULT NULL,
  `priority` varchar(20) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `time` time DEFAULT NULL,
  `mobile_number` varchar(15) DEFAULT NULL,
  `email_id` varchar(100) DEFAULT NULL,
  `father_name` varchar(100) DEFAULT NULL,
  `father_cnic_number` varchar(15) DEFAULT NULL,
  `mother_name` varchar(100) DEFAULT NULL,
  `mother_cnic_number` varchar(15) DEFAULT NULL,
  `spouse_name` varchar(100) DEFAULT NULL,
  `spouse_cnic_number` varchar(15) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `present_address` text DEFAULT NULL,
  `permanent_address` text DEFAULT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  `status` varchar(20) DEFAULT 'Pending',
  `user_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tokenslip`
--

INSERT INTO `tokenslip` (`id`, `tracking_id`, `name`, `cnic_number`, `application_type`, `priority`, `date`, `time`, `mobile_number`, `email_id`, `father_name`, `father_cnic_number`, `mother_name`, `mother_cnic_number`, `spouse_name`, `spouse_cnic_number`, `date_of_birth`, `present_address`, `permanent_address`, `image_path`, `status`, `user_id`) VALUES
(3878, '770006339276', 'صبا ءطالب', '31103-3319930-8', 'شناختی کارڈ میں ترمیم', 'نارمل', '2024-03-02', '09:31:00', '03086876515', '<EMAIL>', 'طالب حسین', '31103-9291072-3', 'پروین اختر ', '31103-5096490-6', 'عدنان ارشد ', '31103-3198940-9', '1995-12-12', 'بستی جنوبی ڈاکخانہ خاص چک نمبر 308/ایچ آر', 'بستی جنوبی ڈاکخانہ خاص چک نمبر 308/ایچ آر', 'uploads/ggff.jpg', 'Submitted', NULL),
(3879, '770006336607', 'محمد حبیب الرحمٰن', '31103-6544961-5', 'شناختی کارڈ میں ترمیم', 'ایگزیکٹیو', '2024-03-01', '11:41:00', '03446441419', '<EMAIL>', 'محمّد اختر ', '', 'نصرت بی بی ', '31103-1100524-2', 'بشریٰ اقبال', '31103-3832874-4', '1998-09-01', 'ہاوس نمبر 684اے ایڈن لین ولاز 2 ٹرائی اینگل سائیڈ امیر پور', 'ڈاکخانہ مروٹ چک نمبر 319/ایچ آر', 'uploads/1709276539318.jpg', 'Submitted', NULL),
(3880, '770006334257', 'محمد بوٹا', '31103-7592335-9', 'شناختی کارڈ میں ترمیم', 'نارمل', '2024-02-29', '10:54:00', '+923027050412', '<EMAIL>', 'محمّد الیاس', '', 'شمیم بی بی', '31103-8205053-6', 'ثوبیہ بی بی', '31103-9906724-4', '1983-06-15', 'ڈاکخانہ مروٹ کالونی چک نمبر 312/ایچ آر', 'ڈاکخانہ مروٹ کالونی چک نمبر 312/ایچ آر', 'uploads/1709199131465.jpg', 'Submitted', NULL),
(3881, '770006344006', 'فقیر حسین', '31103-3680840-9', 'شناختی کارڈ میں ترمیم', 'نارمل', '2024-03-04', '10:07:00', '03008683319', '<EMAIL>', 'احسان الحق', '31103-1111650-1', 'صفیہ بی بی ', '31103-7363756-4', 'ماریہ رمضان', '31103-5010800-6', '1995-12-12', 'ڈاکخانہ مروٹ چک نمبر 319/ایچ آر', 'ڈاکخانہ مروٹ چک نمبر 319/ایچ آر', 'uploads/1709528638051.jpg', 'Submitted', NULL),
(3882, '770006344102', 'شگفتہ بیگم', '31103-1085188-2', 'شناختی کارڈ میں ترمیم', 'نارمل', '2024-03-04', '10:37:00', '03088875358', '<EMAIL>', 'محمد اکرم', '31103-6905270-9', 'رشیدہ بیگم', '311032427088-2', 'نذر حسین', '31103-1119294-5', '1968-01-01', 'ڈاکخانہ مروٹ چک نمبر 313/ایچ آر', 'ڈاکخانہ مروٹ چک نمبر 313/ایچ آر', 'uploads/1709530563530.jpg', 'Submitted', NULL),
(3883, '770006329830', 'اقبال حسین', '31103-9199919-5', 'گمشدہ', 'ایگزیکٹیو', '2024-02-28', '11:50:00', '03251542319', '<EMAIL>', '', '', '', '', '', '', '2024-03-04', '', '', 'uploads/1709103096759.jpg', 'Submitted', NULL),


-- --------------------------------------------------------

--
-- Table structure for table `user_activity_logs`
--

CREATE TABLE `user_activity_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(50) DEFAULT NULL,
  `record_id` int(11) DEFAULT NULL,
  `old_values` text DEFAULT NULL,
  `new_values` text DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_activity_logs`
--

INSERT INTO `user_activity_logs` (`id`, `user_id`, `action`, `table_name`, `record_id`, `old_values`, `new_values`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 0, 'login', NULL, NULL, NULL, NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-24 08:38:48'),
(2, 0, 'logout', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-24 08:55:06'),
(3, 0, 'login', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-27 04:47:02'),
(4, 0, 'logout', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-27 04:47:52'),
(5, 0, 'login', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-27 04:51:11');

-- --------------------------------------------------------

--
-- Table structure for table `user_permissions`
--

CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `permission` varchar(50) NOT NULL,
  `granted_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_permissions`
--

INSERT INTO `user_permissions` (`id`, `user_id`, `permission`, `granted_by`, `created_at`) VALUES
(1, 1, 'view_all_records', 1, '2025-06-24 08:18:49'),
(2, 1, 'edit_all_records', 1, '2025-06-24 08:18:49'),
(3, 1, 'delete_all_records', 1, '2025-06-24 08:18:49'),
(4, 1, 'manage_users', 1, '2025-06-24 08:18:49'),
(5, 1, 'view_reports', 1, '2025-06-24 08:18:49'),
(6, 1, 'system_settings', 1, '2025-06-24 08:18:49'),
(7, 1, 'view_logs', 1, '2025-06-24 08:18:49'),
(8, 0, 'view_own_records', 1, '2025-06-24 08:18:49'),
(9, 0, 'edit_own_records', 1, '2025-06-24 08:18:49');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `application_types`
--
ALTER TABLE `application_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_urdu_name` (`urdu_name`),
  ADD UNIQUE KEY `unique_english_name` (`english_name`);

--
-- Indexes for table `emails`
--
ALTER TABLE `emails`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_expires` (`expires_at`);

--
-- Indexes for table `priorities`
--
ALTER TABLE `priorities`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_urdu_name` (`urdu_name`),
  ADD UNIQUE KEY `unique_english_name` (`english_name`);

--
-- Indexes for table `tokenslip`
--
ALTER TABLE `tokenslip`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- Indexes for table `user_activity_logs`
--
ALTER TABLE `user_activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_user_action` (`user_id`,`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_permissions`
--
ALTER TABLE `user_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_permission` (`user_id`,`permission`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_granted_by` (`granted_by`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `application_types`
--
ALTER TABLE `application_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `emails`
--
ALTER TABLE `emails`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=480;

--
-- AUTO_INCREMENT for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `priorities`
--
ALTER TABLE `priorities`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `tokenslip`
--
ALTER TABLE `tokenslip`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4295;

--
-- AUTO_INCREMENT for table `user_activity_logs`
--
ALTER TABLE `user_activity_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `user_permissions`
--
ALTER TABLE `user_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
