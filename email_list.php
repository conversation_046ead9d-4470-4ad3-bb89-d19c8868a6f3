<?php
include('session.php');
include('db_connection.php');
include('header.php');
?>

<style>
    :root {
        --primary-color: #2c5530;
        --secondary-color: #4a7c59;
        --accent-color: #f8f9fa;
        --text-dark: #2d3436;
        --border-color: #e9ecef;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .main-container {
        max-width: 1400px;
        margin: 0 auto;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .header-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
    }

    .header-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    }

    .header-section h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .header-section .subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 10px;
        position: relative;
        z-index: 1;
    }

    .content-section {
        padding: 40px;
    }

    .search-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .search-title {
        color: var(--primary-color);
        font-weight: 700;
        margin-bottom: 20px;
        font-size: 1.3rem;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-control, .form-select {
        border: 2px solid var(--border-color);
        border-radius: 12px;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
        outline: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border: none;
        color: white;
        padding: 12px 25px;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
        color: white;
    }

    .btn-success {
        background: var(--success-color);
        border: none;
        color: white;
        padding: 8px 15px;
        font-size: 0.85rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-success:hover {
        background: #218838;
        transform: translateY(-1px);
        color: white;
    }

    .btn-warning {
        background: var(--warning-color);
        border: none;
        color: #212529;
        padding: 8px 15px;
        font-size: 0.85rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-warning:hover {
        background: #e0a800;
        transform: translateY(-1px);
        color: #212529;
    }

    .btn-danger {
        background: var(--danger-color);
        border: none;
        color: white;
        padding: 8px 15px;
        font-size: 0.85rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        background: #c82333;
        transform: translateY(-1px);
        color: white;
    }

    .btn-light {
        background: white;
        border: 2px solid var(--border-color);
        color: var(--text-dark);
        padding: 10px 20px;
        font-size: 0.9rem;
        font-weight: 600;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .btn-light:hover {
        background: var(--accent-color);
        border-color: var(--primary-color);
        color: var(--primary-color);
        transform: translateY(-1px);
    }

    .table-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .table-responsive {
        border-radius: 12px;
        overflow-x: auto;
        overflow-y: visible;
        max-width: 100%;
    }

    .table {
        margin-bottom: 0;
        font-size: 0.9rem;
        min-width: 800px;
    }

    .table th {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        font-weight: 600;
        border: none;
        padding: 15px 10px;
        position: sticky;
        top: 0;
        z-index: 10;
        white-space: nowrap;
    }

    .table td {
        padding: 12px 10px;
        vertical-align: middle;
        border-bottom: 1px solid var(--border-color);
    }

    .table tbody tr:hover {
        background-color: rgba(44, 85, 48, 0.05);
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-active {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-inactive {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
    }

    .pagination {
        justify-content: center;
        margin-top: 30px;
    }

    .page-link {
        color: var(--primary-color);
        border: 2px solid var(--border-color);
        border-radius: 8px;
        margin: 0 2px;
        padding: 10px 15px;
        font-weight: 500;
    }

    .page-link:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .export-section {
        text-align: right;
        margin-bottom: 20px;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .main-container {
            margin: 10px;
            border-radius: 15px;
        }

        .header-section {
            padding: 20px;
        }

        .header-section h1 {
            font-size: 2rem;
        }

        .content-section {
            padding: 25px;
        }

        .search-card {
            padding: 20px;
        }

        .table-card {
            padding: 15px;
        }

        .table {
            font-size: 0.8rem;
            min-width: 700px;
        }

        .table th,
        .table td {
            padding: 8px 6px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 3px;
        }

        .action-buttons .btn {
            font-size: 0.75rem;
            padding: 6px 10px;
        }

        .export-section {
            text-align: center;
            margin-bottom: 15px;
        }
    }

    /* Animation */
    .search-card, .table-card {
        animation: slideInUp 0.6s ease forwards;
        opacity: 0;
        transform: translateY(30px);
    }

    .search-card { animation-delay: 0.1s; }
    .table-card { animation-delay: 0.2s; }

    @keyframes slideInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Custom scrollbar */
    .table-responsive::-webkit-scrollbar {
        height: 8px;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: var(--secondary-color);
    }
</style>
<<div class="main-container">
    <!-- Header Section -->
    <div class="header-section">
        <h1><i class="fas fa-envelope-open-text me-3"></i>Email Management</h1>
        <p class="subtitle">Manage Email List & Communication Settings</p>
    </div>

    <!-- Content Section -->
    <div class="content-section">
        <!-- Export Section -->
        <div class="export-section">
            <button type="button" class="btn btn-light" onclick="exportToExcel()">
                <i class="fas fa-file-excel me-2"></i>Export to Excel
            </button>
        </div>

        <!-- Search Section -->
        <div class="search-card">
            <h3 class="search-title">
                <i class="fas fa-search"></i>
                Search & Filter Emails
            </h3>
            <form method="GET" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <input type="text" class="form-control" name="search"
                               placeholder="Search by email address..."
                               value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
                <div class="col-md-3">
                    <a href="email_list.php" class="btn btn-light w-100">
                        <i class="fas fa-refresh me-2"></i>Clear
                    </a>
                </div>
            </form>
        </div>

        <!-- Data Table -->
        <div class="table-card">

            <div class="table-responsive">
                <table class="table table-hover" id="emailTable">
                    <thead>
                        <tr>
                            <th style="width: 80px;">#</th>
                            <th style="width: 300px;">Email Address</th>
                            <th style="width: 120px;">Status</th>
                            <th style="width: 180px;">Created Date</th>
                            <th style="width: 200px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                                <?php
                                $limit = 15;
                                $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                                $offset = ($page - 1) * $limit;

                                $search = isset($_GET['search']) ? $_GET['search'] : '';
                                $where_clause = '';
                                $params = [];
                                $types = '';

                                if (!empty($search)) {
                                    $where_clause = "WHERE email LIKE ?";
                                    $search_param = "%$search%";
                                    $params = [$search_param];
                                    $types = "s";
                                }

                                // Count total records
                                $count_sql = "SELECT COUNT(*) as total FROM email_list $where_clause";
                                if (!empty($params)) {
                                    $stmt = $conn->prepare($count_sql);
                                    $stmt->bind_param($types, ...$params);
                                    $stmt->execute();
                                    $total_result = $stmt->get_result();
                                } else {
                                    $total_result = $conn->query($count_sql);
                                }
                                
                                $total_records = $total_result->fetch_assoc()['total'];
                                $total_pages = ceil($total_records / $limit);

                                // Fetch records
                                $sql = "SELECT * FROM email_list $where_clause ORDER BY id DESC LIMIT ? OFFSET ?";
                                $types .= "ii";
                                array_push($params, $limit, $offset);

                                $stmt = $conn->prepare($sql);
                                $stmt->bind_param($types, ...$params);
                                $stmt->execute();
                                $result = $stmt->get_result();

                        if ($result->num_rows > 0) {
                            $counter = $offset + 1;
                            while ($row = $result->fetch_assoc()) {
                                echo "<tr>";
                                echo "<td><strong>" . $counter++ . "</strong></td>";
                                echo "<td>";
                                echo "<div class='d-flex align-items-center'>";
                                echo "<i class='fas fa-envelope text-primary me-2'></i>";
                                echo "<a href='mailto:" . htmlspecialchars($row['email']) . "' class='text-decoration-none'>";
                                echo htmlspecialchars($row['email']);
                                echo "</a>";
                                echo "</div>";
                                echo "</td>";
                                echo "<td>";
                                echo "<span class='status-badge " . ($row['status'] ? 'status-active' : 'status-inactive') . "'>";
                                echo "<i class='fas " . ($row['status'] ? 'fa-check-circle' : 'fa-times-circle') . " me-1'></i>";
                                echo ($row['status'] ? 'Active' : 'Inactive');
                                echo "</span>";
                                echo "</td>";
                                echo "<td>";
                                echo "<div class='d-flex align-items-center'>";
                                echo "<i class='fas fa-calendar text-muted me-2'></i>";
                                echo "<span>" . date('M d, Y', strtotime($row['created_at'])) . "</span><br>";
                                echo "<small class='text-muted'>" . date('h:i A', strtotime($row['created_at'])) . "</small>";
                                echo "</div>";
                                echo "</td>";
                                echo "<td>";
                                echo "<div class='action-buttons'>";
                                echo "<button onclick='toggleStatus(" . $row['id'] . ", " . $row['status'] . ")' ";
                                echo "class='btn " . ($row['status'] ? 'btn-warning' : 'btn-success') . "' ";
                                echo "title='" . ($row['status'] ? 'Deactivate Email' : 'Activate Email') . "'>";
                                echo "<i class='fas " . ($row['status'] ? 'fa-ban' : 'fa-check') . " me-1'></i>";
                                echo ($row['status'] ? 'Deactivate' : 'Activate');
                                echo "</button>";
                                echo "<button onclick='deleteEmail(" . $row['id'] . ")' class='btn btn-danger' title='Delete Email'>";
                                echo "<i class='fas fa-trash me-1'></i>Delete";
                                echo "</button>";
                                echo "</div>";
                                echo "</td>";
                                echo "</tr>";
                            }
                        } else {
                            echo "<tr>";
                            echo "<td colspan='5' class='text-center py-5'>";
                            echo "<div class='text-muted'>";
                            echo "<i class='fas fa-envelope-open fa-3x mb-3'></i>";
                            echo "<h5>No Emails Found</h5>";
                            echo "<p>No email addresses match your search criteria.</p>";
                            echo "</div>";
                            echo "</td>";
                            echo "</tr>";
                        }
                                ?>
                            </tbody>
                        </table>
                    </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($page-1); ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);
                    $search_param = !empty($search) ? '&search=' . urlencode($search) : '';

                    if ($start_page > 1) {
                        echo "<li class='page-item'><a class='page-link' href='?page=1$search_param'>1</a></li>";
                        if ($start_page > 2) {
                            echo "<li class='page-item disabled'><span class='page-link'>...</span></li>";
                        }
                    }

                    for ($i = $start_page; $i <= $end_page; $i++) {
                        $active = ($i == $page) ? 'active' : '';
                        echo "<li class='page-item $active'>";
                        echo "<a class='page-link' href='?page=$i$search_param'>$i</a>";
                        echo "</li>";
                    }

                    if ($end_page < $total_pages) {
                        if ($end_page < $total_pages - 1) {
                            echo "<li class='page-item disabled'><span class='page-link'>...</span></li>";
                        }
                        echo "<li class='page-item'><a class='page-link' href='?page=$total_pages$search_param'>$total_pages</a></li>";
                    }
                    ?>

                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($page+1); ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>

            <!-- Records Info -->
            <div class="mt-3 text-center text-muted">
                <small>
                    Showing <?php echo (($page-1) * $limit + 1); ?> to
                    <?php echo min($page * $limit, $total_records); ?> of
                    <?php echo $total_records; ?> email addresses
                </small>
            </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Excel Export Library -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

    <script>
    $(document).ready(function() {
        // Add smooth animations
        const elements = document.querySelectorAll('.search-card, .table-card');
        elements.forEach((el, index) => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            setTimeout(() => {
                el.style.transition = 'all 0.6s ease';
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // Add hover effects to action buttons
        $('.btn').hover(
            function() { $(this).addClass('shadow-sm'); },
            function() { $(this).removeClass('shadow-sm'); }
        );

        // Add loading state to buttons
        $('.btn').on('click', function() {
            const btn = $(this);
            if (!btn.hasClass('no-loading')) {
                const originalText = btn.html();
                btn.html('<i class="fas fa-spinner fa-spin me-1"></i>Loading...');
                setTimeout(() => {
                    btn.html(originalText);
                }, 2000);
            }
        });
    });

    function toggleStatus(id, currentStatus) {
        const action = currentStatus ? 'deactivate' : 'activate';
        const message = `Are you sure you want to ${action} this email address?`;

        if (confirm(message)) {
            // Add loading state
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
            btn.disabled = true;

            // Redirect after short delay for visual feedback
            setTimeout(() => {
                window.location.href = 'toggle_email_status.php?id=' + id;
            }, 500);
        }
    }

    function deleteEmail(id) {
        const message = 'Are you sure you want to delete this email address? This action cannot be undone.';

        if (confirm(message)) {
            // Add loading state
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Deleting...';
            btn.disabled = true;

            // Redirect after short delay for visual feedback
            setTimeout(() => {
                window.location.href = 'delete_email_list.php?id=' + id;
            }, 500);
        }
    }

    function exportToExcel() {
        try {
            // Show loading state
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
            btn.disabled = true;

            // Get table data
            const table = document.getElementById('emailTable');
            const wb = XLSX.utils.table_to_book(table, {
                sheet: "Email List",
                raw: false
            });

            // Generate filename with current date
            const now = new Date();
            const dateStr = now.toISOString().split('T')[0];
            const filename = `email_list_${dateStr}.xlsx`;

            // Export file
            XLSX.writeFile(wb, filename);

            // Show success message
            setTimeout(() => {
                btn.innerHTML = '<i class="fas fa-check me-1"></i>Exported!';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
            }, 1000);

        } catch (error) {
            console.error('Export failed:', error);
            alert('Export failed. Please try again.');

            // Reset button
            const btn = event.target;
            btn.innerHTML = '<i class="fas fa-file-excel me-2"></i>Export to Excel';
            btn.disabled = false;
        }
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            document.querySelector('input[name="search"]').focus();
        }
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportToExcel();
        }
    });

    // Auto-refresh status indicators every 30 seconds
    setInterval(function() {
        const statusBadges = document.querySelectorAll('.status-badge');
        statusBadges.forEach(badge => {
            badge.style.opacity = '0.7';
            setTimeout(() => {
                badge.style.opacity = '1';
            }, 200);
        });
    }, 30000);
    </script>
</body>
</html>
<?php $conn->close(); ?>
