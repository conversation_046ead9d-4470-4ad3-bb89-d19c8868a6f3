<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apply Online - Pakistan Identity Card System</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .header-section .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 10px;
            position: relative;
            z-index: 1;
        }

        .content-section {
            padding: 40px;
        }

        .info-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .info-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-text {
            color: var(--text-dark);
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .iframe-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .iframe-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 15px 20px;
            margin: -20px -20px 20px -20px;
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .iframe-title {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .loading-indicator {
            display: none;
            text-align: center;
            padding: 40px;
            color: var(--secondary-color);
        }

        .loading-indicator i {
            font-size: 2rem;
            margin-bottom: 15px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .nadra-iframe {
            width: 100%;
            height: 1000px;
            border: none;
            border-radius: 12px;
            background: white;
        }

        .back-button {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            font-size: 0.9rem;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 124, 89, 0.3);
            color: white;
            text-decoration: none;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-section {
                padding: 20px;
            }

            .header-section h1 {
                font-size: 2rem;
            }

            .content-section {
                padding: 25px;
            }

            .info-card {
                padding: 20px;
            }

            .iframe-container {
                padding: 15px;
            }

            .iframe-header {
                margin: -15px -15px 15px -15px;
                padding: 12px 15px;
            }

            .nadra-iframe {
                height: 800px;
            }
        }

        @media (max-width: 480px) {
            .header-section h1 {
                font-size: 1.8rem;
            }

            .content-section {
                padding: 20px;
            }

            .info-card {
                padding: 15px;
            }

            .nadra-iframe {
                height: 600px;
            }
        }

        /* Animation */
        .info-card, .iframe-container {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .info-card { animation-delay: 0.1s; }
        .iframe-container { animation-delay: 0.2s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <?php include 'header.php'; ?>

    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1><i class="fas fa-globe me-3"></i>Apply Online</h1>
            <p class="subtitle">Official NADRA Portal for Pakistan Identity Card Applications</p>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Information Card -->
            <div class="info-card">
                <h3 class="info-title">
                    <i class="fas fa-info-circle"></i>
                    Important Information
                </h3>
                <p class="info-text">
                    You are being redirected to the official NADRA (National Database and Registration Authority) portal.
                    This is the secure government website for Pakistan Identity Card applications and services.
                </p>
                <p class="info-text">
                    <strong>Please ensure:</strong>
                </p>
                <ul class="info-text">
                    <li>You have all required documents ready</li>
                    <li>Your internet connection is stable</li>
                    <li>You complete the application in one session</li>
                    <li>You save/print your application receipt</li>
                </ul>
                <div class="mt-3">
                    <a href="index.php" class="back-button">
                        <i class="fas fa-arrow-left"></i>
                        Back to Main Form
                    </a>
                </div>
            </div>

            <!-- NADRA Portal Frame -->
            <div class="iframe-container">
                <div class="iframe-header">
                    <h4 class="iframe-title">
                        <i class="fas fa-external-link-alt"></i>
                        NADRA Official Portal
                    </h4>
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Secure Connection
                    </small>
                </div>

                <div class="loading-indicator" id="loadingIndicator">
                    <i class="fas fa-spinner"></i>
                    <p>Loading NADRA Portal...</p>
                    <small class="text-muted">Please wait while we connect you to the official website</small>
                </div>

                <iframe
                    src="https://id.nadra.gov.pk/e-id/authenticate"
                    class="nadra-iframe"
                    id="nadraFrame"
                    allowfullscreen
                    onload="hideLoading()"
                    onerror="showError()">
                </iframe>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Show loading indicator initially
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('nadraFrame').style.display = 'none';
        });

        // Hide loading indicator when iframe loads
        function hideLoading() {
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('nadraFrame').style.display = 'block';
        }

        // Show error message if iframe fails to load
        function showError() {
            document.getElementById('loadingIndicator').innerHTML = `
                <i class="fas fa-exclamation-triangle text-warning"></i>
                <p class="text-warning">Unable to load NADRA Portal</p>
                <small class="text-muted">Please check your internet connection or try again later</small>
                <br><br>
                <a href="https://id.nadra.gov.pk/e-id/authenticate" target="_blank" class="btn btn-primary">
                    <i class="fas fa-external-link-alt me-2"></i>
                    Open in New Tab
                </a>
            `;
        }

        // Handle iframe communication (if needed)
        window.addEventListener('message', function(event) {
            // Handle messages from NADRA iframe if needed
            if (event.origin === 'https://id.nadra.gov.pk') {
                console.log('Message from NADRA:', event.data);
            }
        });
    </script>
</body>
</html>
