<?php
// Debug script to find the exact issue
session_start();
include 'db_connection.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Login Debug - Pakistan ID System</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .container { max-width: 900px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; }
    .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0; }
    .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0; }
    .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0; }
    table { width: 100%; border-collapse: collapse; margin: 15px 0; }
    th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
    th { background-color: #f8f9fa; font-weight: bold; }
    .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
    code { background: #f8f9fa; padding: 2px 5px; border-radius: 3px; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔍 Login Issue Debug Report</h1>";
echo "<p>Detailed analysis of login and session issues</p>";

// Section 1: Database Connection
echo "<div class='section'>";
echo "<h2>1. Database Connection Status</h2>";
if ($conn->connect_error) {
    echo "<div class='error'>❌ Database connection failed: " . $conn->connect_error . "</div>";
    exit();
} else {
    echo "<div class='success'>✅ Database connected successfully</div>";
    echo "<p><strong>Database:</strong> " . $conn->get_server_info() . "</p>";
}
echo "</div>";

// Section 2: Current Session Info
echo "<div class='section'>";
echo "<h2>2. Current Session Information</h2>";
if (isset($_SESSION['user_id'])) {
    echo "<div class='info'>";
    echo "<p><strong>Session Active:</strong> ✅ Yes</p>";
    echo "<p><strong>User ID:</strong> " . $_SESSION['user_id'] . "</p>";
    echo "<p><strong>Username:</strong> " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'Not set') . "</p>";
    echo "<p><strong>Last Activity:</strong> " . (isset($_SESSION['last_activity']) ? date('Y-m-d H:i:s', $_SESSION['last_activity']) : 'Not set') . "</p>";
    echo "</div>";
} else {
    echo "<div class='warning'>⚠️ No active session found</div>";
}
echo "</div>";

// Section 3: All Tables in Database
echo "<div class='section'>";
echo "<h2>3. Database Tables Analysis</h2>";
$result = $conn->query("SHOW TABLES");
if ($result->num_rows > 0) {
    echo "<table>";
    echo "<tr><th>Table Name</th><th>Type</th><th>Records</th></tr>";
    
    $login_tables = [];
    while ($row = $result->fetch_array()) {
        $table_name = $row[0];
        
        // Count records
        $count_result = $conn->query("SELECT COUNT(*) as count FROM " . $table_name);
        $count = $count_result->fetch_assoc()['count'];
        
        echo "<tr>";
        echo "<td>" . $table_name . "</td>";
        
        // Check if it's a login-related table
        if (stripos($table_name, 'login') !== false || stripos($table_name, 'user') !== false || stripos($table_name, 'admin') !== false) {
            echo "<td style='color: green; font-weight: bold;'>🔐 LOGIN TABLE</td>";
            $login_tables[] = $table_name;
        } else {
            echo "<td>📋 Data Table</td>";
        }
        
        echo "<td>" . $count . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (empty($login_tables)) {
        echo "<div class='error'>❌ No login tables found!</div>";
    }
} else {
    echo "<div class='error'>❌ No tables found in database!</div>";
}
echo "</div>";

// Section 4: Login Tables Structure
if (!empty($login_tables)) {
    echo "<div class='section'>";
    echo "<h2>4. Login Tables Structure & Data</h2>";
    
    foreach ($login_tables as $table) {
        echo "<h3>Table: " . $table . "</h3>";
        
        // Show structure
        echo "<h4>Structure:</h4>";
        $structure = $conn->query("DESCRIBE " . $table);
        echo "<table>";
        echo "<tr><th>Field</th><th>Type</th><th>Key</th></tr>";
        while ($field = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $field['Field'] . "</td>";
            echo "<td>" . $field['Type'] . "</td>";
            echo "<td>" . $field['Key'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Show data (hide passwords)
        echo "<h4>Data:</h4>";
        $data = $conn->query("SELECT * FROM " . $table);
        if ($data->num_rows > 0) {
            echo "<table>";
            $fields = $data->fetch_fields();
            echo "<tr>";
            foreach ($fields as $field) {
                echo "<th>" . $field->name . "</th>";
            }
            echo "</tr>";
            
            while ($row = $data->fetch_assoc()) {
                echo "<tr>";
                foreach ($row as $key => $value) {
                    if (stripos($key, 'password') !== false) {
                        echo "<td>[HIDDEN]</td>";
                    } else {
                        echo "<td>" . htmlspecialchars($value) . "</td>";
                    }
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='warning'>⚠️ No data found in " . $table . "</div>";
        }
    }
    echo "</div>";
}

// Section 5: Session User Verification
if (isset($_SESSION['user_id']) && !empty($login_tables)) {
    echo "<div class='section'>";
    echo "<h2>5. Session User Verification</h2>";
    
    $user_found = false;
    foreach ($login_tables as $table) {
        echo "<h3>Checking table: " . $table . "</h3>";
        
        try {
            $stmt = $conn->prepare("SELECT * FROM " . $table . " WHERE id = ?");
            $stmt->bind_param("i", $_SESSION['user_id']);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $user = $result->fetch_assoc();
                echo "<div class='success'>";
                echo "<p>✅ User found in table: <strong>" . $table . "</strong></p>";
                echo "<table>";
                foreach ($user as $key => $value) {
                    if (stripos($key, 'password') === false) {
                        echo "<tr><td><strong>" . $key . "</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
                    }
                }
                echo "</table>";
                echo "</div>";
                $user_found = true;
            } else {
                echo "<div class='error'>❌ User ID " . $_SESSION['user_id'] . " not found in " . $table . "</div>";
            }
            $stmt->close();
        } catch (Exception $e) {
            echo "<div class='error'>Error checking " . $table . ": " . $e->getMessage() . "</div>";
        }
    }
    
    if (!$user_found) {
        echo "<div class='error'>";
        echo "<h3>🚨 PROBLEM IDENTIFIED!</h3>";
        echo "<p>Session User ID <strong>" . $_SESSION['user_id'] . "</strong> does not exist in any login table.</p>";
        echo "<p><strong>Solutions:</strong></p>";
        echo "<ol>";
        echo "<li>Logout and login again</li>";
        echo "<li>Clear browser cookies</li>";
        echo "<li>Check if login is happening from a different table</li>";
        echo "</ol>";
        echo "</div>";
    }
    echo "</div>";
}

// Section 6: Login Process Analysis
echo "<div class='section'>";
echo "<h2>6. Login Process Analysis</h2>";
echo "<p>Let's check what happens during login...</p>";

// Check login.php for the exact query
$login_file = 'login.php';
if (file_exists($login_file)) {
    $login_content = file_get_contents($login_file);
    
    // Extract the SELECT query
    if (preg_match('/SELECT.*FROM\s+(\w+)\s+WHERE/i', $login_content, $matches)) {
        $login_table = $matches[1];
        echo "<div class='info'>";
        echo "<p>✅ Login process uses table: <strong>" . $login_table . "</strong></p>";
        echo "<p>Query found: <code>" . htmlspecialchars($matches[0]) . "</code></p>";
        echo "</div>";
        
        // Check if this table exists and has data
        $check_table = $conn->query("SELECT COUNT(*) as count FROM " . $login_table);
        if ($check_table) {
            $count = $check_table->fetch_assoc()['count'];
            echo "<p>Records in " . $login_table . ": <strong>" . $count . "</strong></p>";
        }
    } else {
        echo "<div class='warning'>⚠️ Could not extract login table from login.php</div>";
    }
} else {
    echo "<div class='error'>❌ login.php file not found</div>";
}
echo "</div>";

// Section 7: Recommendations
echo "<div class='section'>";
echo "<h2>7. Recommendations</h2>";
echo "<div class='info'>";
echo "<h3>🎯 Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Immediate Fix:</strong> <a href='logout.php'>Logout</a> and <a href='login.php'>Login Again</a></li>";
echo "<li><strong>Clear Session:</strong> Clear browser cookies and cache</li>";
echo "<li><strong>Reset Admin:</strong> Use <a href='reset_admin_password.php'>Emergency Reset</a></li>";
echo "<li><strong>Create Fresh Admin:</strong> Run <a href='setup_admin.php'>Admin Setup</a></li>";
echo "</ol>";
echo "</div>";
echo "</div>";

$conn->close();
echo "</div>";
echo "</body></html>";
?>
