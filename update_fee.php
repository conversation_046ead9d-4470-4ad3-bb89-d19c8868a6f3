<?php
session_start();
include 'header.php';
include_once 'db_connection.php';

// Initialize response variables
$success = false;
$error_message = '';
$success_message = '';

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        // Validate and sanitize form data
        $application_for = trim($_POST['application_for'] ?? '');
        $priority = trim($_POST['priority'] ?? '');
        $fee = floatval($_POST['fee'] ?? 0);
        $service_fee = floatval($_POST['service_fee'] ?? 0);
        $total_fee = floatval($_POST['total_fee'] ?? 0);

        // Validation
        if (empty($application_for)) {
            throw new Exception("Please select application type");
        }

        if (empty($priority)) {
            throw new Exception("Please select priority");
        }

        if ($fee < 0) {
            throw new Exception("Fee cannot be negative");
        }

        if ($service_fee < 0) {
            throw new Exception("Service fee cannot be negative");
        }

        // Verify total calculation
        $calculated_total = $fee + $service_fee;
        if (abs($total_fee - $calculated_total) > 0.01) {
            $total_fee = $calculated_total; // Auto-correct if there's a minor discrepancy
        }

        // Check if fee table exists, if not create it
        $create_table_sql = "CREATE TABLE IF NOT EXISTS fee (
            id INT AUTO_INCREMENT PRIMARY KEY,
            application_type VARCHAR(100) NOT NULL,
            priority VARCHAR(50) NOT NULL,
            fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            service_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            total_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_fee (application_type, priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if (!$conn->query($create_table_sql)) {
            throw new Exception("Error creating fee table: " . $conn->error);
        }

        // Prepare SQL statement to insert or update fee record
        $stmt = $conn->prepare("INSERT INTO fee (application_type, priority, fee, service_fee, total_fee) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE fee = VALUES(fee), service_fee = VALUES(service_fee), total_fee = VALUES(total_fee)");

        if (!$stmt) {
            throw new Exception("Prepare statement failed: " . $conn->error);
        }

        $stmt->bind_param("ssddd", $application_for, $priority, $fee, $service_fee, $total_fee);

        // Execute the statement
        if ($stmt->execute()) {
            $success = true;
            $success_message = "Fee record updated/added successfully!";

            // Log the action (optional)
            error_log("Fee updated: Application: $application_for, Priority: $priority, Fee: $fee, Service Fee: $service_fee, Total: $total_fee");
        } else {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        // Close statement
        $stmt->close();

    } catch (Exception $e) {
        $error_message = $e->getMessage();
        error_log("Fee update error: " . $e->getMessage());
    }
}

// Close connection
$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fee Update Result</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .result-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            animation: slideInUp 0.6s ease;
        }

        .result-header {
            padding: 30px;
            text-align: center;
            color: white;
        }

        .result-header.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
        }

        .result-header.error {
            background: linear-gradient(135deg, var(--danger-color) 0%, #e74c3c 100%);
        }

        .result-header h1 {
            font-size: 2.5rem;
            margin: 0;
            font-weight: 700;
        }

        .result-header .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: bounce 1s ease infinite;
        }

        .result-body {
            padding: 40px;
            text-align: center;
        }

        .result-message {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .btn-action {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        }

        .btn-secondary:hover {
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.3);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .result-container {
                margin: 20px;
                border-radius: 15px;
            }

            .result-header {
                padding: 20px;
            }

            .result-header h1 {
                font-size: 2rem;
            }

            .result-header .icon {
                font-size: 3rem;
            }

            .result-body {
                padding: 25px;
            }

            .result-message {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="result-container">
        <?php if ($success): ?>
            <!-- Success Result -->
            <div class="result-header success">
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h1>Success!</h1>
            </div>
            <div class="result-body">
                <div class="result-message text-success">
                    <i class="fas fa-thumbs-up me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
                <div class="action-buttons">
                    <a href="fee.php" class="btn-action">
                        <i class="fas fa-plus me-2"></i>
                        Add New Fee
                    </a>
                    <a href="index.php" class="btn-action btn-secondary">
                        <i class="fas fa-home me-2"></i>
                        Home Page
                    </a>
                </div>
            </div>
        <?php elseif (!empty($error_message)): ?>
            <!-- Error Result -->
            <div class="result-header error">
                <div class="icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h1>Error!</h1>
            </div>
            <div class="result-body">
                <div class="result-message text-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
                <div class="action-buttons">
                    <a href="fee.php" class="btn-action">
                        <i class="fas fa-arrow-left me-2"></i>
                        Go Back
                    </a>
                    <a href="index.php" class="btn-action btn-secondary">
                        <i class="fas fa-home me-2"></i>
                        Home Page
                    </a>
                </div>
            </div>
        <?php else: ?>
            <!-- No Data Submitted -->
            <div class="result-header error">
                <div class="icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <h1>No Data</h1>
            </div>
            <div class="result-body">
                <div class="result-message text-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    No data was submitted
                </div>
                <div class="action-buttons">
                    <a href="fee.php" class="btn-action">
                        <i class="fas fa-edit me-2"></i>
                        Fee Form
                    </a>
                    <a href="index.php" class="btn-action btn-secondary">
                        <i class="fas fa-home me-2"></i>
                        Home Page
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Auto redirect after success (optional) -->
    <?php if ($success): ?>
    <script>
        // Auto redirect to fee form after 5 seconds
        setTimeout(function() {
            if (confirm('Would you like to add another fee record?')) {
                window.location.href = 'fee.php';
            }
        }, 3000);
    </script>
    <?php endif; ?>
</body>
</html>
