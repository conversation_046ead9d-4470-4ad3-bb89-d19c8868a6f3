<?php
// This is a standalone script to reset admin password
// Use this only when you forget the admin password

// Include database connection
include 'db_connection.php';

$message = '';
$error = '';

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST["new_password"]) && isset($_POST["confirm_password"]) && isset($_POST["security_key"])) {
        $new_password = $_POST["new_password"];
        $confirm_password = $_POST["confirm_password"];
        $security_key = $_POST["security_key"];
        
        // Simple security check - change this key for your security
        $correct_security_key = "RESET_ADMIN_2025"; // Change this to your own secret key
        
        // Validate inputs
        if ($security_key !== $correct_security_key) {
            $error = "Invalid security key";
        } elseif (empty($new_password) || empty($confirm_password)) {
            $error = "All fields are required";
        } elseif ($new_password !== $confirm_password) {
            $error = "New password and confirm password do not match";
        } elseif (strlen($new_password) < 6) {
            $error = "New password must be at least 6 characters long";
        } else {
            // Hash new password
            $hashed_new_password = password_hash($new_password, PASSWORD_DEFAULT);
            
            // Update admin password in database
            $stmt = $conn->prepare("UPDATE login SET password = ? WHERE username = 'admin'");
            $stmt->bind_param("s", $hashed_new_password);
            
            if ($stmt->execute()) {
                if ($stmt->affected_rows > 0) {
                    $message = "Admin password reset successfully! You can now login with the new password.";
                } else {
                    $error = "Admin user not found in database";
                }
            } else {
                $error = "Error updating password: " . $conn->error;
            }
            $stmt->close();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Admin Password - Pakistan Identity Card System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #dc3545;
            --secondary-color: #c82333;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .reset-container {
            max-width: 500px;
            width: 100%;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .reset-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .reset-header h2 {
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
        }

        .reset-header .subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-top: 10px;
        }

        .reset-body {
            padding: 40px;
        }

        .warning-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            text-align: center;
        }

        .warning-box i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-control {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
            outline: none;
        }

        .input-group {
            position: relative;
        }

        .input-group .form-control {
            padding-left: 50px;
        }

        .input-group-text {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--primary-color);
            z-index: 3;
            font-size: 1.1rem;
        }

        .btn-reset {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(220, 53, 69, 0.3);
            color: white;
        }

        .btn-login {
            background: #6c757d;
            border: none;
            color: white;
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
            width: 100%;
            text-align: center;
        }

        .btn-login:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }

        .alert {
            border-radius: 12px;
            padding: 15px 20px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #c3e6cb;
            color: #155724;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 2px solid #f5c6cb;
            color: #721c24;
        }

        .security-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        /* Animation */
        .reset-container {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .reset-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .reset-header {
                padding: 20px;
            }
            
            .reset-body {
                padding: 25px 20px;
            }
            
            .form-control {
                padding: 12px 15px;
            }
            
            .input-group .form-control {
                padding-left: 45px;
            }
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <h2><i class="fas fa-exclamation-triangle me-3"></i>Reset Admin Password</h2>
            <p class="subtitle">Emergency Password Reset Tool</p>
        </div>
        
        <div class="reset-body">
            <div class="warning-box">
                <i class="fas fa-shield-alt"></i>
                <strong>Security Warning!</strong><br>
                This tool should only be used when you have forgotten the admin password.
                Make sure you are authorized to perform this action.
            </div>

            <?php if (!empty($message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
                <a href="login.php" class="btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>Go to Login Page
                </a>
            <?php else: ?>
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="resetForm">
                    <div class="form-group">
                        <label for="security_key" class="form-label">
                            <i class="fas fa-shield-alt"></i>
                            Security Key
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-shield-alt"></i></span>
                            <input type="password" id="security_key" name="security_key" class="form-control"
                                   placeholder="Enter security key" required>
                        </div>
                        <div class="security-info">
                            <strong>Note:</strong> Contact your system administrator for the security key.
                            Default key is: <code>RESET_ADMIN_2025</code>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="new_password" class="form-label">
                            <i class="fas fa-key"></i>
                            New Admin Password
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-key"></i></span>
                            <input type="password" id="new_password" name="new_password" class="form-control"
                                   placeholder="Enter new password" required minlength="6">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password" class="form-label">
                            <i class="fas fa-check-circle"></i>
                            Confirm New Password
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-check-circle"></i></span>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control"
                                   placeholder="Confirm new password" required minlength="6">
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn-reset" id="resetBtn">
                            <i class="fas fa-key me-2"></i>
                            Reset Admin Password
                        </button>
                    </div>
                </form>

                <a href="login.php" class="btn-login">
                    <i class="fas fa-arrow-left me-2"></i>Back to Login
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Password confirmation validation
        document.getElementById('resetForm')?.addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('New password and confirm password do not match!');
                return false;
            }
            
            if (newPassword.length < 6) {
                e.preventDefault();
                alert('Password must be at least 6 characters long!');
                return false;
            }
            
            // Confirm action
            if (!confirm('Are you sure you want to reset the admin password? This action cannot be undone.')) {
                e.preventDefault();
                return false;
            }
            
            // Add loading state
            const resetBtn = document.getElementById('resetBtn');
            resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Resetting...';
            resetBtn.disabled = true;
        });

        // Real-time password match validation
        document.getElementById('confirm_password')?.addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && newPassword !== confirmPassword) {
                this.style.borderColor = '#dc3545';
            } else {
                this.style.borderColor = '#e9ecef';
            }
        });
    </script>
</body>
</html>

<?php $conn->close(); ?>
