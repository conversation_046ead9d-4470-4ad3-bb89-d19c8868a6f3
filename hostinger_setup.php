<?php
// Hostinger Server Setup Script
// Run this after uploading files to Hostinger

echo "<!DOCTYPE html>";
echo "<html><head><title>Hostinger Setup - Pakistan ID System</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
    table { width: 100%; border-collapse: collapse; margin: 15px 0; }
    th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
    th { background-color: #f8f9fa; font-weight: bold; }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
    .btn:hover { background: #0056b3; }
    .step { background: #e9ecef; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🚀 Hostinger Server Setup</h1>";
echo "<p>Pakistan Identity Card System - Server Configuration</p>";

// Step 1: Check database connection
echo "<div class='step'>";
echo "<h2>Step 1: Database Connection Test</h2>";

// Try to connect with current settings
$servername = "localhost";
$username = "u372178158_PAKID";
$password = "YOUR_DATABASE_PASSWORD"; // This needs to be updated
$database = "u372178158_PAKID";

echo "<p><strong>Current Settings:</strong></p>";
echo "<ul>";
echo "<li><strong>Server:</strong> " . $servername . "</li>";
echo "<li><strong>Username:</strong> " . $username . "</li>";
echo "<li><strong>Database:</strong> " . $database . "</li>";
echo "<li><strong>Password:</strong> " . (($password == "YOUR_DATABASE_PASSWORD") ? "❌ NOT SET" : "✅ SET") . "</li>";
echo "</ul>";

if ($password == "YOUR_DATABASE_PASSWORD") {
    echo "<div class='warning'>";
    echo "<h3>⚠️ Database Password Not Set!</h3>";
    echo "<p>Please update the database password in <code>db_connection.php</code> file:</p>";
    echo "<ol>";
    echo "<li>Open <code>db_connection.php</code> in file manager or FTP</li>";
    echo "<li>Find line: <code>\$password = \"YOUR_DATABASE_PASSWORD\";</code></li>";
    echo "<li>Replace <code>YOUR_DATABASE_PASSWORD</code> with your actual database password</li>";
    echo "<li>Save the file and refresh this page</li>";
    echo "</ol>";
    echo "<p><strong>How to find your database password:</strong></p>";
    echo "<ul>";
    echo "<li>Login to Hostinger hPanel</li>";
    echo "<li>Go to Databases → MySQL Databases</li>";
    echo "<li>Find your database: u372178158_PAKID</li>";
    echo "<li>Copy the password or reset it if needed</li>";
    echo "</ul>";
    echo "</div>";
} else {
    // Try connection
    $conn = new mysqli($servername, $username, $password, $database);
    
    if ($conn->connect_error) {
        echo "<div class='error'>";
        echo "<h3>❌ Database Connection Failed!</h3>";
        echo "<p><strong>Error:</strong> " . $conn->connect_error . "</p>";
        echo "<p><strong>Common Solutions:</strong></p>";
        echo "<ul>";
        echo "<li>Check if database password is correct</li>";
        echo "<li>Verify database name: u372178158_PAKID</li>";
        echo "<li>Make sure database exists in Hostinger hPanel</li>";
        echo "<li>Check if database user has proper permissions</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='success'>";
        echo "<h3>✅ Database Connection Successful!</h3>";
        echo "<p>Connected to database: " . $database . "</p>";
        echo "</div>";
        
        // Step 2: Check tables
        echo "</div><div class='step'>";
        echo "<h2>Step 2: Database Tables Check</h2>";
        
        $result = $conn->query("SHOW TABLES");
        if ($result->num_rows > 0) {
            echo "<div class='success'>";
            echo "<p>✅ Found " . $result->num_rows . " tables in database:</p>";
            echo "<table>";
            echo "<tr><th>Table Name</th><th>Status</th></tr>";
            
            $has_login_table = false;
            while ($row = $result->fetch_array()) {
                $table_name = $row[0];
                echo "<tr><td>" . $table_name . "</td>";
                
                if (strtolower($table_name) == 'login') {
                    $has_login_table = true;
                    echo "<td style='color: green;'>✅ Login Table</td>";
                } else {
                    echo "<td>📋 Data Table</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
            
            // Step 3: Check/Create admin user
            echo "</div><div class='step'>";
            echo "<h2>Step 3: Admin User Setup</h2>";
            
            if ($has_login_table) {
                // Check if admin user exists
                $stmt = $conn->prepare("SELECT id, username FROM login WHERE username = 'admin'");
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows == 0) {
                    echo "<div class='warning'>";
                    echo "<p>⚠️ Admin user not found. Creating admin user...</p>";
                    echo "</div>";
                    
                    // Create admin user
                    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
                    $insert_stmt = $conn->prepare("INSERT INTO login (username, password) VALUES (?, ?)");
                    $insert_stmt->bind_param("ss", $admin_username = 'admin', $admin_password);
                    
                    if ($insert_stmt->execute()) {
                        echo "<div class='success'>";
                        echo "<h3>✅ Admin User Created Successfully!</h3>";
                        echo "<p><strong>Username:</strong> admin</p>";
                        echo "<p><strong>Password:</strong> admin123</p>";
                        echo "<p><strong>⚠️ Important:</strong> Change this password after first login!</p>";
                        echo "</div>";
                    } else {
                        echo "<div class='error'>";
                        echo "<p>❌ Error creating admin user: " . $conn->error . "</p>";
                        echo "</div>";
                    }
                    $insert_stmt->close();
                } else {
                    $admin = $result->fetch_assoc();
                    echo "<div class='success'>";
                    echo "<h3>✅ Admin User Already Exists</h3>";
                    echo "<p><strong>User ID:</strong> " . $admin['id'] . "</p>";
                    echo "<p><strong>Username:</strong> " . $admin['username'] . "</p>";
                    echo "</div>";
                }
                $stmt->close();
            } else {
                echo "<div class='error'>";
                echo "<p>❌ Login table not found! Please create the login table first.</p>";
                echo "<p><a href='create_login_table.php' class='btn'>Create Login Table</a></p>";
                echo "</div>";
            }
        } else {
            echo "<div class='warning'>";
            echo "<p>⚠️ No tables found in database. Please run table creation scripts.</p>";
            echo "</div>";
        }
        
        $conn->close();
    }
}

echo "</div>";

// Step 4: Next Steps
echo "<div class='step'>";
echo "<h2>Step 4: Next Steps</h2>";
echo "<div class='info'>";
echo "<h3>🎯 Quick Actions:</h3>";
echo "<p><a href='login.php' class='btn'>🔐 Go to Login Page</a></p>";
echo "<p><a href='update_admin_password.php' class='btn'>🔑 Update Admin Password</a></p>";
echo "<p><a href='index.php' class='btn'>🏠 Go to Dashboard</a></p>";
echo "</div>";

echo "<div class='info'>";
echo "<h3>📋 Setup Checklist:</h3>";
echo "<ol>";
echo "<li>✅ Upload all files to Hostinger</li>";
echo "<li>" . (($password != "YOUR_DATABASE_PASSWORD") ? "✅" : "❌") . " Update database password in db_connection.php</li>";
echo "<li>✅ Run this setup script</li>";
echo "<li>🔄 Test login with admin/admin123</li>";
echo "<li>🔄 Change admin password</li>";
echo "<li>🔄 Test all functionality</li>";
echo "</ol>";
echo "</div>";

echo "<div class='warning'>";
echo "<h3>🔒 Security Recommendations:</h3>";
echo "<ul>";
echo "<li>Change default admin password immediately</li>";
echo "<li>Delete this setup file after successful setup</li>";
echo "<li>Keep database credentials secure</li>";
echo "<li>Regular backup of database</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
