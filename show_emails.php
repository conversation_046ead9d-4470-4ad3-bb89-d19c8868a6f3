<?php
session_start();

// If this is a new record (detected by success message in session)
if (isset($_SESSION['success_message'])) {
    // Redirect to first page to see the new record
    header("Location: show_emails.php?page=1");
    unset($_SESSION['success_message']);
    exit();
}

include 'header.php';
include_once 'db_connection.php';

// Function to get image as base64
function getImageBase64($imagePath) {
    $fullPath = __DIR__ . '/images/' . $imagePath;
    
    if (!file_exists($fullPath)) {
        return false;
    }
    
    try {
        $imageData = file_get_contents($fullPath);
        if ($imageData === false) {
            return false;
        }
        
        $base64 = base64_encode($imageData);
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $fullPath);
        finfo_close($finfo);
        
        return 'data:' . $mimeType . ';base64,' . $base64;
    } catch (Exception $e) {
        return false;
    }
}

// Initialize search query
$search_query = isset($_GET['search']) ? $_GET['search'] : "";
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Prepare search query
$search_condition = "";
if (!empty($search_query)) {
    $search_condition = "WHERE email LIKE '%$search_query%' 
                         OR verifier_cnic LIKE '%$search_query%' 
                         OR name LIKE '%$search_query%' 
                         OR father_name LIKE '%$search_query%' 
                         OR husband_name LIKE '%$search_query%' 
                         OR phone_number LIKE '%$search_query%'";
}

// Count total records
$count_sql = "SELECT COUNT(*) as total FROM emails $search_condition";
$count_result = $conn->query($count_sql);
$total_records = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_records / $records_per_page);

// Fetch paginated data with ORDER BY id DESC
$sql = "SELECT * FROM emails 
        $search_condition 
        ORDER BY id DESC 
        LIMIT $records_per_page OFFSET $offset";
$result = $conn->query($sql);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verifiers List - Pakistan Identity Card System</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .header-section .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 10px;
            position: relative;
            z-index: 1;
        }

        .content-section {
            padding: 40px;
        }

        .search-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .search-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
            outline: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            border: none;
            color: white;
            padding: 12px 25px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
            color: white;
        }

        .table-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            overflow: hidden;
        }

        .table-responsive {
            border-radius: 12px;
            overflow-x: auto;
            overflow-y: visible;
            max-width: 100%;
        }

        .table {
            margin-bottom: 0;
            font-size: 0.9rem;
            min-width: 800px;
        }

        .table th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 15px 10px;
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
        }

        .table td {
            padding: 12px 10px;
            vertical-align: middle;
            border-bottom: 1px solid var(--border-color);
        }

        .table tbody tr:hover {
            background-color: rgba(44, 85, 48, 0.05);
        }

        .profile-container {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid var(--primary-color);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .profile-container:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .profile-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .default-avatar {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .action-btn {
            padding: 8px 12px;
            font-size: 0.85rem;
            border-radius: 8px;
            border: none;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-edit {
            background: #007bff;
            color: white;
        }

        .btn-delete {
            background: var(--danger-color);
            color: white;
        }

        .pagination {
            justify-content: center;
            margin-top: 30px;
        }

        .page-link {
            color: var(--primary-color);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            margin: 0 2px;
            padding: 10px 15px;
            font-weight: 500;
        }

        .page-link:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-section {
                padding: 20px;
            }

            .header-section h1 {
                font-size: 2rem;
            }

            .content-section {
                padding: 25px;
            }

            .search-card {
                padding: 20px;
            }

            .table-card {
                padding: 15px;
            }

            .table {
                font-size: 0.8rem;
                min-width: 700px;
            }

            .table th,
            .table td {
                padding: 8px 6px;
            }

            .profile-container {
                width: 40px;
                height: 40px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 3px;
            }

            .action-btn {
                font-size: 0.75rem;
                padding: 6px 10px;
            }

            .search-form {
                flex-direction: column;
                gap: 10px;
            }

            .search-form .form-control {
                margin-bottom: 10px;
            }
        }

        @media (max-width: 480px) {
            .header-section h1 {
                font-size: 1.8rem;
            }

            .content-section {
                padding: 20px;
            }

            .search-card {
                padding: 15px;
            }

            .table {
                min-width: 600px;
            }
        }

        /* Animation */
        .search-card, .table-card {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .search-card { animation-delay: 0.1s; }
        .table-card { animation-delay: 0.2s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Custom scrollbar */
        .table-responsive::-webkit-scrollbar {
            height: 8px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        /* Image Preview Modal */
        .image-preview {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            z-index: 9999;
            background: white;
            padding: 15px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            display: none;
            max-width: 90vw;
            max-height: 90vh;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .image-preview img {
            max-width: 400px;
            max-height: 400px;
            object-fit: contain;
            border-radius: 10px;
            display: block;
        }

        .image-preview .close-btn {
            position: absolute;
            top: -10px;
            right: -10px;
            background: var(--danger-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .image-preview .image-info {
            text-align: center;
            margin-top: 10px;
            color: var(--text-dark);
            font-size: 0.9rem;
        }

        .image-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 9998;
            display: none;
            backdrop-filter: blur(5px);
            opacity: 0;
            transition: opacity 0.3s ease;
            cursor: pointer;
        }
    </style>
</head>
<body>
<div class="main-container">
    <!-- Header Section -->
    <div class="header-section">
        <h1><i class="fas fa-users me-3"></i>Verifiers List</h1>
        <p class="subtitle">Manage Pakistan Identity Card Verifiers</p>
    </div>

    <!-- Content Section -->
    <div class="content-section">
        <!-- Search Section -->
        <div class="search-card">
            <h3 class="search-title">
                <i class="fas fa-search"></i>
                Search & Filter Verifiers
            </h3>
            <div class="row">
                <div class="col-md-8">
                    <form class="d-flex search-form" action="" method="GET">
                        <input type="search" name="search" class="form-control"
                               placeholder="Search by name, CNIC, email, phone..."
                               value="<?php echo htmlspecialchars($search_query); ?>">
                        <button class="btn btn-primary ms-2" type="submit">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                    </form>
                </div>
                <div class="col-md-4 text-end">
                    <a href="submit_email.php" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Add New Verifier
                    </a>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <div class="table-card">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 80px;">ID</th>
                            <th style="width: 80px;">Profile</th>
                            <th style="width: 200px;">Name</th>
                            <th style="width: 150px;">CNIC</th>
                            <th style="width: 200px;">Email</th>
                            <th style="width: 150px;">Phone</th>
                            <th style="width: 120px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if ($result->num_rows > 0) {
                            while($row = $result->fetch_assoc()) {
                                echo "<tr>";
                                echo "<td><strong>" . htmlspecialchars($row["id"]) . "</strong></td>";
                                echo "<td>";
                                echo "<div class='profile-container preview-image' data-name='" . htmlspecialchars($row["name"]) . "' data-cnic='" . htmlspecialchars($row["verifier_cnic"]) . "'>";
                                if (!empty($row['image_path'])) {
                                    $imageData = getImageBase64($row['image_path']);
                                    if ($imageData) {
                                        echo "<img src='" . $imageData . "' class='profile-img' alt='Profile'>";
                                    } else {
                                        echo '<div class="default-avatar">
                                                <i class="fas fa-user"></i>
                                              </div>';
                                    }
                                } else {
                                    echo '<div class="default-avatar">
                                            <i class="fas fa-user"></i>
                                          </div>';
                                }
                                echo "</div>";
                                echo "</td>";
                                echo "<td><strong>" . htmlspecialchars($row["name"]) . "</strong></td>";
                                echo "<td><span class='text-muted'>" . htmlspecialchars($row["verifier_cnic"]) . "</span></td>";
                                echo "<td><a href='mailto:" . htmlspecialchars($row["email"]) . "' class='text-decoration-none'>" . htmlspecialchars($row["email"]) . "</a></td>";
                                echo "<td><a href='tel:" . htmlspecialchars($row["phone_number"]) . "' class='text-decoration-none'>" . htmlspecialchars($row["phone_number"]) . "</a></td>";
                                echo "<td>";
                                echo "<div class='action-buttons'>";
                                echo "<a href='edit_email.php?id=" . $row["id"] . "' class='action-btn btn-edit' title='Edit Verifier'>";
                                echo "<i class='fas fa-edit'></i>";
                                echo "</a>";
                                echo "<button onclick='confirmDelete(" . $row["id"] . ")' class='action-btn btn-delete' title='Delete Verifier'>";
                                echo "<i class='fas fa-trash'></i>";
                                echo "</button>";
                                echo "</div>";
                                echo "</td>";
                                echo "</tr>";
                            }
                        } else {
                            echo "<tr>";
                            echo "<td colspan='7' class='text-center py-5'>";
                            echo "<div class='text-muted'>";
                            echo "<i class='fas fa-users fa-3x mb-3'></i>";
                            echo "<h5>No Verifiers Found</h5>";
                            echo "<p>No verifiers match your search criteria.</p>";
                            echo "</div>";
                            echo "</td>";
                            echo "</tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($page-1); ?>&search=<?php echo urlencode($search_query); ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);

                    if ($start_page > 1) {
                        echo "<li class='page-item'><a class='page-link' href='?page=1&search=" . urlencode($search_query) . "'>1</a></li>";
                        if ($start_page > 2) {
                            echo "<li class='page-item disabled'><span class='page-link'>...</span></li>";
                        }
                    }

                    for ($i = $start_page; $i <= $end_page; $i++) {
                        $active = ($i == $page) ? 'active' : '';
                        echo "<li class='page-item $active'>";
                        echo "<a class='page-link' href='?page=$i&search=" . urlencode($search_query) . "'>$i</a>";
                        echo "</li>";
                    }

                    if ($end_page < $total_pages) {
                        if ($end_page < $total_pages - 1) {
                            echo "<li class='page-item disabled'><span class='page-link'>...</span></li>";
                        }
                        echo "<li class='page-item'><a class='page-link' href='?page=$total_pages&search=" . urlencode($search_query) . "'>$total_pages</a></li>";
                    }
                    ?>

                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($page+1); ?>&search=<?php echo urlencode($search_query); ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>

            <!-- Records Info -->
            <div class="mt-3 text-center text-muted">
                <small>
                    Showing <?php echo (($page-1) * $records_per_page + 1); ?> to
                    <?php echo min($page * $records_per_page, $total_records); ?> of
                    <?php echo $total_records; ?> verifiers
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Image Preview Modal -->
<div class="image-overlay" id="imageOverlay"></div>
<div class="image-preview" id="imagePreview">
    <button class="close-btn" onclick="closeImagePreview()">
        <i class="fas fa-times"></i>
    </button>
    <img id="previewImage" src="" alt="Preview">
    <div class="image-info" id="imageInfo"></div>
</div>

<!-- Bootstrap 5 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
$(document).ready(function() {
    // Add hover effects to action buttons
    $('.action-btn').hover(
        function() { $(this).addClass('shadow-sm'); },
        function() { $(this).removeClass('shadow-sm'); }
    );

    // Image preview functionality
    $('.preview-image').on('click', function() {
        const imageSrc = $(this).find('img').attr('src');
        const userName = $(this).data('name');
        const userCnic = $(this).data('cnic');

        if (imageSrc) {
            showImagePreview(imageSrc, userName, userCnic);
        }
    });

    // Add smooth animations
    const elements = document.querySelectorAll('.search-card, .table-card');
    elements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        setTimeout(() => {
            el.style.transition = 'all 0.6s ease';
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

function confirmDelete(id) {
    if (confirm('Are you sure you want to delete this verifier? This action cannot be undone.')) {
        window.location.href = 'delete_email.php?id=' + id;
    }
}

// Image preview functions
function showImagePreview(imageSrc, userName, userCnic) {
    const overlay = document.getElementById('imageOverlay');
    const preview = document.getElementById('imagePreview');
    const previewImage = document.getElementById('previewImage');
    const imageInfo = document.getElementById('imageInfo');

    // Set image source and info
    previewImage.src = imageSrc;
    imageInfo.innerHTML = `
        <strong>${userName}</strong><br>
        <small>CNIC: ${userCnic}</small>
    `;

    // Show overlay and preview
    overlay.style.display = 'block';
    preview.style.display = 'block';

    // Add animation
    setTimeout(() => {
        overlay.style.opacity = '1';
        preview.style.opacity = '1';
        preview.style.transform = 'translate(-50%, -50%) scale(1)';
    }, 10);

    // Close on overlay click
    overlay.onclick = closeImagePreview;

    // Close on Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeImagePreview();
        }
    });
}

function closeImagePreview() {
    const overlay = document.getElementById('imageOverlay');
    const preview = document.getElementById('imagePreview');

    // Add closing animation
    overlay.style.opacity = '0';
    preview.style.opacity = '0';
    preview.style.transform = 'translate(-50%, -50%) scale(0.8)';

    // Hide after animation
    setTimeout(() => {
        overlay.style.display = 'none';
        preview.style.display = 'none';
        preview.style.transform = 'translate(-50%, -50%) scale(1)';
    }, 300);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        document.querySelector('input[name="search"]').focus();
    }
});
</script>
</body>
</html>

<?php
// Close database connection
$conn->close();
?>
