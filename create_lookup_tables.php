<?php
// Include database connection
include_once 'db_connection.php';

try {
    // Create application_types table
    $sql_app_types = "CREATE TABLE IF NOT EXISTS application_types (
        id INT AUTO_INCREMENT PRIMARY KEY,
        urdu_name VARCHAR(100) NOT NULL,
        english_name VARCHAR(100) NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_urdu_name (urdu_name),
        UNIQUE KEY unique_english_name (english_name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql_app_types)) {
        echo "Application types table created successfully<br>";
    } else {
        throw new Exception("Error creating application_types table: " . $conn->error);
    }

    // Create priorities table
    $sql_priorities = "CREATE TABLE IF NOT EXISTS priorities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        urdu_name VARCHAR(50) NOT NULL,
        english_name VARCHAR(50) NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_urdu_name (urdu_name),
        UNIQUE KEY unique_english_name (english_name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql_priorities)) {
        echo "Priorities table created successfully<br>";
    } else {
        throw new Exception("Error creating priorities table: " . $conn->error);
    }

    // Insert default application types
    $app_types_data = [
        ['شناختی کارڈ میں ترمیم', 'ID Card Modification'],
        ['رینو', 'Renewal'],
        ['گمشدہ', 'Lost Card'],
        ['فیملی سرٹیفکیٹ', 'Family Certificate'],
        ['سمارٹ شناختی کارڈ', 'Smart ID Card'],
        ['شناختی کارڈمنسوخ', 'ID Card Cancellation']
    ];

    $stmt_app = $conn->prepare("INSERT IGNORE INTO application_types (urdu_name, english_name) VALUES (?, ?)");
    foreach ($app_types_data as $app_type) {
        $stmt_app->bind_param("ss", $app_type[0], $app_type[1]);
        $stmt_app->execute();
    }
    $stmt_app->close();
    echo "Application types data inserted successfully<br>";

    // Insert default priorities
    $priorities_data = [
        ['نارمل', 'Normal'],
        ['ارجنٹ', 'Urgent'],
        ['ایگزیکٹیو', 'Executive']
    ];

    $stmt_priority = $conn->prepare("INSERT IGNORE INTO priorities (urdu_name, english_name) VALUES (?, ?)");
    foreach ($priorities_data as $priority) {
        $stmt_priority->bind_param("ss", $priority[0], $priority[1]);
        $stmt_priority->execute();
    }
    $stmt_priority->close();
    echo "Priorities data inserted successfully<br>";

    echo "<br><strong>Lookup tables setup completed successfully!</strong>";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
} finally {
    $conn->close();
}
?>
