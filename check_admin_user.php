<?php
// Debug script to check admin user status
include 'db_connection.php';

echo "<h2>Admin User Debug Information</h2>";

// Check if login table exists
$result = $conn->query("SHOW TABLES LIKE 'login'");
if ($result->num_rows == 0) {
    echo "<p style='color: red;'>❌ Login table does not exist!</p>";
    echo "<p>Please run create_login_table.php first.</p>";
} else {
    echo "<p style='color: green;'>✅ Login table exists</p>";
    
    // Check table structure
    echo "<h3>Table Structure:</h3>";
    $result = $conn->query("DESCRIBE login");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check all users in login table
    echo "<h3>All Users in Login Table:</h3>";
    $result = $conn->query("SELECT id, username, created_at FROM login");
    if ($result->num_rows == 0) {
        echo "<p style='color: red;'>❌ No users found in login table!</p>";
        echo "<p>Creating default admin user...</p>";
        
        // Create default admin user
        $admin_username = 'admin';
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO login (username, password) VALUES (?, ?)");
        $stmt->bind_param("ss", $admin_username, $admin_password);
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ Default admin user created successfully!</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
        } else {
            echo "<p style='color: red;'>❌ Error creating admin user: " . $conn->error . "</p>";
        }
        $stmt->close();
        
        // Show users again
        $result = $conn->query("SELECT id, username, created_at FROM login");
    }
    
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Created At</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['username'] . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

// Check current session
echo "<h3>Current Session Information:</h3>";
session_start();
if (isset($_SESSION['user_id'])) {
    echo "<p><strong>Session User ID:</strong> " . $_SESSION['user_id'] . "</p>";
    echo "<p><strong>Session Username:</strong> " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'Not set') . "</p>";
    
    // Verify user exists in database
    $stmt = $conn->prepare("SELECT id, username FROM login WHERE id = ?");
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 1) {
        $user = $result->fetch_assoc();
        echo "<p style='color: green;'>✅ Session user exists in database</p>";
        echo "<p><strong>Database User ID:</strong> " . $user['id'] . "</p>";
        echo "<p><strong>Database Username:</strong> " . $user['username'] . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Session user does not exist in database!</p>";
        echo "<p>This is why you're getting 'User not found' error.</p>";
        echo "<p>Please logout and login again.</p>";
    }
    $stmt->close();
} else {
    echo "<p style='color: orange;'>⚠️ No active session found</p>";
    echo "<p>Please login first.</p>";
}

$conn->close();
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>
