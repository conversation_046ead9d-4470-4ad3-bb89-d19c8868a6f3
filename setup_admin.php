<?php
// Simple admin setup script
include 'db_connection.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Admin Setup</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:40px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>Admin User Setup</h1>";

// Check if login table exists
$result = $conn->query("SHOW TABLES LIKE 'login'");
if ($result->num_rows == 0) {
    echo "<p class='error'>❌ Login table does not exist. Creating it now...</p>";
    
    // Create login table
    $sql = "CREATE TABLE IF NOT EXISTS login (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql)) {
        echo "<p class='success'>✅ Login table created successfully</p>";
    } else {
        echo "<p class='error'>❌ Error creating login table: " . $conn->error . "</p>";
        exit();
    }
} else {
    echo "<p class='success'>✅ Login table exists</p>";
}

// Check if admin user exists
$stmt = $conn->prepare("SELECT id, username FROM login WHERE username = 'admin'");
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    echo "<p class='info'>ℹ️ Admin user does not exist. Creating it now...</p>";
    
    // Create admin user
    $admin_username = 'admin';
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    
    $insert_stmt = $conn->prepare("INSERT INTO login (username, password) VALUES (?, ?)");
    $insert_stmt->bind_param("ss", $admin_username, $admin_password);
    
    if ($insert_stmt->execute()) {
        echo "<p class='success'>✅ Admin user created successfully!</p>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
        echo "<p class='info'>Please change this password after first login!</p>";
    } else {
        echo "<p class='error'>❌ Error creating admin user: " . $conn->error . "</p>";
    }
    $insert_stmt->close();
} else {
    $admin = $result->fetch_assoc();
    echo "<p class='success'>✅ Admin user already exists</p>";
    echo "<p><strong>User ID:</strong> " . $admin['id'] . "</p>";
    echo "<p><strong>Username:</strong> " . $admin['username'] . "</p>";
}

$stmt->close();

// Show all users
echo "<h2>All Users in System:</h2>";
$result = $conn->query("SELECT id, username, created_at FROM login ORDER BY id");
if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
    echo "<tr style='background:#f0f0f0;'><th style='padding:10px;'>ID</th><th style='padding:10px;'>Username</th><th style='padding:10px;'>Created At</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td style='padding:10px;'>" . $row['id'] . "</td>";
        echo "<td style='padding:10px;'>" . $row['username'] . "</td>";
        echo "<td style='padding:10px;'>" . $row['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='error'>No users found in the system!</p>";
}

echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li><a href='login.php'>Go to Login Page</a></li>";
echo "<li><a href='update_admin_password.php'>Update Admin Password</a> (after login)</li>";
echo "<li><a href='check_admin_user.php'>Debug User Issues</a></li>";
echo "</ol>";

$conn->close();
echo "</body></html>";
?>
